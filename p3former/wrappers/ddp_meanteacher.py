import torch
import numpy as np
from typing import Any, Dict, Union
# from torch.nn.parallel import DistributedDataParallel

from mmengine.registry import MODEL_WRAPPERS
from mmengine.optim import OptimWrapper
from mmdet3d.structures import PointData
from mmengine.model.wrappers import MMDistributedDataParallel
from mmengine.model.utils import detect_anomalous_params
from mmdet.models.utils import multi_apply

from tools.heatmap_generation import regen_hm_UDA

@MODEL_WRAPPERS.register_module()
class MMDistributedDataParallel_MT(MMDistributedDataParallel):
    def __init__(self, module, device_ids=None, output_device=None, **kwargs):
        super().__init__(module, device_ids=device_ids, output_device=output_device, **kwargs)
        # self.ema_decay = data['data_samples']['ema_decay']
        self.ema_decay = self.module.ema_decay
        
    def train_step(self, data: Union[dict, tuple, list],
                   optim_wrapper: OptimWrapper) -> Dict[str, torch.Tensor]:
        """Interface for model forward, backward and parameters updating during
        training process.

        :meth:`train_step` will perform the following steps in order:

        - If :attr:`module` defines the preprocess method,
          call ``module.preprocess`` to pre-processing data.
        - Call ``module.forward(**data)`` and get losses.
        - Parse losses.
        - Call ``optim_wrapper.optimizer_step`` to update parameters.
        - Return log messages of losses.

        Args:
            data (dict or tuple or list): Data sampled from dataset.
            optim_wrapper (OptimWrapper): A wrapper of optimizer to
                update parameters.

        Returns:
            Dict[str, torch.Tensor]: A ``dict`` of tensor for logging.
        """
        
        labeled_data = data if True in data.get('is_labeled', False) else None
        unlabeled_data = data if True not in data.get('is_labeled', False) else None

        # # Process labeled data
        if labeled_data:
            with optim_wrapper.optim_context(self):
                labeled_data['data_samples'] = self.set_datasample_mode(labeled_data['data_samples'], 
                                                                          'source', 
                                                                          {'gen_hm_by': 'GT', 
                                                                           'save_query_update': False,
                                                                           'do_PL_loss': False})
                labeled_data = self.module.data_preprocessor(labeled_data, True)
                losses = self.module.stud_model.forward(
                    labeled_data['inputs'], labeled_data['data_samples'], mode='loss') 

        # Process unlabeled data
        if unlabeled_data:
            # $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ teacher forward $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
            with torch.no_grad():
                if len(self.module.dn_pl_aug) > 0 or len(self.module.dn_pl_grow) > 0:
                    unlabeled_data_copy = unlabeled_data.copy()
                
                # generate PL
                unlabeled_data['data_samples'] = self.set_datasample_mode(unlabeled_data['data_samples'], 
                                                                          'teacher', 
                                                                          {'gen_hm_by': 'GT', # currently no pred PL, use GT to generate hm, but not effect the teacher model
                                                                           'save_query_update': False,
                                                                           'do_PL_pred': True})
                unlabeled_data = self.module.teac_model.data_preprocessor(unlabeled_data, True) 
                teacher_outputs = self.module.teac_model.forward(
                    unlabeled_data['inputs'], unlabeled_data['data_samples'], mode='predict'
                )
            
            if len(self.module.dn_pl_aug) > 0 or len(self.module.dn_pl_grow) > 0:
                unlabeled_data_copy['inputs']['imgs'] = unlabeled_data_copy['inputs']['img']
                for i, data_sample in enumerate(unlabeled_data['data_samples']):
                    #TODO-YINING: here convert PL to numpy, may have performance issue!!!!
                    #TODO-YINING: convert torch process may fasten the processes.
                    data, device = self.pack_data(unlabeled_data_copy, data_sample, i)
                    for aug in self.module.dn_pl_aug:
                        data = aug(data)
                    for grow in self.module.dn_pl_grow:
                        data = grow(data)
                    unlabeled_data_copy = self.update_data(unlabeled_data_copy, data, i, device)
                unlabeled_data = unlabeled_data_copy
                
                # redo the teacher forward, use PL to generate heatmap and voxelization may change due to point augmentations
                unlabeled_data['data_samples'] = self.set_datasample_mode(unlabeled_data['data_samples'], 
                                                                          'teacher', 
                                                                          {'gen_hm_by': 'PL',
                                                                           'save_query_update': True, # save the query if needed
                                                                           'do_PL_pred': False})      # do not predict PL this time, otherwise will overwrite the refined PL
                unlabeled_data = self.module.teac_model.data_preprocessor(unlabeled_data, True)
                teacher_outputs = self.module.teac_model.forward(
                    unlabeled_data['inputs'], unlabeled_data['data_samples'], mode='predict'
                                    )
            # $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
                    
            # $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$ student forward $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
            # apply auxiliary augmentations to target data
            if self.module.student_aux_aug:
                # print(f'!!!!!!! Processing student_aux_aug function in segmentor.py')
                for aug in self.module.student_aux_aug:
                    unlabeled_data['inputs'] = aug(unlabeled_data['inputs'])
            
            # feed target data to student, use PL to supervise
            unlabeled_data['data_samples'] = self.set_datasample_mode(unlabeled_data['data_samples'], 
                                                                      'student', 
                                                                      {'do_PL_loss': True})
            losses = self.module.stud_model.forward(
                unlabeled_data['inputs'], unlabeled_data['data_samples'], mode='loss'
            )
            # $$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
            
            # # check PL quality
            # # save point & PL result & GT
            # import os
            # import numpy as np
            # input, data_samples = unlabeled_data['inputs'], unlabeled_data['data_samples']
            # for i, data_sample in enumerate(data_samples):
            #     token = data_sample.img_metas['token']
            #     point = input['points'][i]
            #     gt_sem = data_sample.gt_pts_seg.pts_semantic_mask
            #     gt_inst = data_sample.gt_pts_seg.pts_instance_mask
            #     pl_sem = data_sample.UDA_pseudo_label.pts_semantic_mask
            #     pl_inst = data_sample.UDA_pseudo_label.pts_instance_mask
            #     pl_score = data_sample.UDA_pseudo_label.pts_sem_score
                
            #     save_folder = f'output/UDA_debug'
            #     os.makedirs(save_folder, exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'gt_sem'), exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'gt_inst'), exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'pl_sem'), exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'pl_inst'), exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'pl_score'), exist_ok=True)
            #     os.makedirs(os.path.join(save_folder, 'point'), exist_ok=True)
                
            #     np.save(os.path.join(save_folder, 'gt_sem', f'{token}.npy'), gt_sem.cpu().numpy())
            #     np.save(os.path.join(save_folder, 'gt_inst', f'{token}.npy'), gt_inst.cpu().numpy())
            #     np.save(os.path.join(save_folder, 'pl_sem', f'{token}.npy'), pl_sem.cpu().numpy())
            #     np.save(os.path.join(save_folder, 'pl_inst', f'{token}.npy'), pl_inst.cpu().numpy())
            #     np.save(os.path.join(save_folder, 'pl_score', f'{token}.npy'), pl_score.cpu().numpy())
            #     np.save(os.path.join(save_folder, 'point', f'{token}.npy'), point.cpu().numpy())
            #     print(f'save {token} done')

        parsed_losses, log_vars = self.module.parse_losses(losses)
        optim_wrapper.update_params(parsed_losses)

        if self.detect_anomalous_params:
            detect_anomalous_params(parsed_losses, model=self)
        
        # Update teacher model parameters using EMA
        self.update_teacher(sync_buffers=self.module.sync_buffers)
        
        return log_vars

    def val_step(self, data, optim_wrapper=None):
        """Perform a training step for semi-supervised learning."""
        # V1: eval both source & target -> current code will cal source & target data together
        # labeled_data = data if True in data.get('is_labeled', False) else None
        # unlabeled_data = data if True not in data.get('is_labeled', False) else None
        
        # V2: eval only target 
        labeled_data = None
        unlabeled_data = data 
        
        # # # Process labeled data
        # if labeled_data:
        #     with torch.no_grad():
        #         labeled_data = self.module.data_preprocessor(labeled_data, False)
                
        #         outputs = self.module.stud_model.forward(
        #             labeled_data['inputs'], labeled_data['data_samples'], mode='predict'
        #         )


        # Process unlabeled data
        if unlabeled_data:
            with torch.no_grad():
                unlabeled_data['data_samples'] = self.set_datasample_mode(unlabeled_data['data_samples'], 
                                                                          'teacher', 
                                                                          {'gen_hm_by': 'GT',
                                                                           'save_query_update': False, 
                                                                           'do_PL_pred': False}) 
                unlabeled_data = self.module.data_preprocessor(unlabeled_data, False)
                outputs = self.module.teac_model.forward(
                    unlabeled_data['inputs'], unlabeled_data['data_samples'], mode='predict'
                )
        return outputs
    
    def test_step(self, data, optim_wrapper=None):
        """Perform a training step for semi-supervised learning."""
        # V1: eval both source & target -> current code will cal source & target data together
        # labeled_data = data if True in data.get('is_labeled', False) else None
        # unlabeled_data = data if True not in data.get('is_labeled', False) else None
        
        # V2: eval only target 
        labeled_data = None
        unlabeled_data = data 
        
        # # # Process labeled data
        # if labeled_data:
        #     with torch.no_grad():
        #         labeled_data = self.module.data_preprocessor(labeled_data, False)
                
        #         outputs = self.module.stud_model.forward(
        #             labeled_data['inputs'], labeled_data['data_samples'], mode='predict'
        #         )


        # Process unlabeled data
        if unlabeled_data:
            with torch.no_grad():
                unlabeled_data['data_samples'] = self.set_datasample_mode(unlabeled_data['data_samples'], 
                                                                          'teacher', 
                                                                          {'gen_hm_by': 'GT',
                                                                           'save_query_update': False, 
                                                                           'do_PL_pred': False})
                unlabeled_data = self.module.data_preprocessor(unlabeled_data, False)
                outputs = self.module.teac_model.forward(
                    unlabeled_data['inputs'], unlabeled_data['data_samples'], mode='predict'
                )
        return outputs
    
    def update_teacher(self, sync_buffers):
        """Update the teacher model's parameters using EMA of student model."""
        for teacher_param, student_param in zip(self.module.teac_model.parameters(), self.module.stud_model.parameters()):
            teacher_param.data.mul_(self.ema_decay).add_((1 - self.ema_decay) * student_param.data)
            
        if sync_buffers:
            for teac_buf, stud_buf in zip(self.module.teac_model.buffers(), self.module.stud_model.buffers()):
                teac_buf.data.copy_(stud_buf.data)
            

    def pack_data(self, unlabeled_data_copy, data_sample, i):
        """Pack data for each frame.
        
        Args:
            unlabeled_data_copy (dict): Original unlabeled data
            data_sample: Data sample containing SAM masks and pseudo labels
            i (int): Frame index
            
        Returns:
            dict: Packed data containing points, images, and SAM masks
        """
        points = unlabeled_data_copy['inputs']['points'][i]
        #TODO-YINING: here change 'img' to 'imgs'
        imgs = unlabeled_data_copy['inputs']['imgs'][i]
        assert isinstance(points, torch.Tensor)
        device = points.device
        points = points.detach().cpu().numpy()
        assert isinstance(imgs, torch.Tensor) and imgs.dim() == 4 and imgs.shape[0] == 6
        imgs = [imgs[j].detach().cpu().numpy() for j in range(6)]
            
        # GT
        gt_sem = data_sample.gt_pts_seg.pts_semantic_mask
        gt_inst = data_sample.gt_pts_seg.pts_instance_mask
        
        # sam mask
        sam_results = dict()
        assert hasattr(data_sample, 'cand_2d') and 'sam_pmask_2d' in data_sample.cand_2d
        for key in ['sam_pmask_2d', 'sam_pscore_2d']:
            value = data_sample.cand_2d[key]
            if isinstance(value, torch.Tensor):
                value = value.detach().cpu().numpy()
            sam_results[key.split('_2d')[0]] = value

        # PL 
        UDA_pseudo_label = dict()
        assert hasattr(data_sample, 'UDA_pseudo_label')
        for key in ['pts_semantic_mask', 'pts_instance_mask', 'pts_sem_score']:
            value = getattr(data_sample.UDA_pseudo_label, key)
            if isinstance(value, torch.Tensor):
                device = value.device
                value = value.detach().cpu().numpy()
            else:
                device = 'numpy'
            UDA_pseudo_label[key] = value
        UDA_pseudo_label['device'] = device
        # UDA_pseudo_label['point2voxel_map'] = data_sample.gt_pts_seg.point2voxel_map #TODO-YINING: may not required
        
        # img metas: lidar2img, ori_shape, scale_factor
        if hasattr(data_sample, 'lidar2img'):
            lidar2img = []
            for l2i in data_sample.lidar2img:
                if isinstance(l2i, torch.Tensor):
                    l2i = l2i.detach().cpu().numpy()
                lidar2img.append(l2i)
            ori_shape = data_sample.img_metas['ori_shape']
            scale_factor = data_sample.metainfo['scale_factor']
        else:
            lidar2img = [None] * 6
            ori_shape = None
            scale_factor = None
            
        # aug_mask
        if hasattr(data_sample, 'augment_mask'):
            aug_mask = data_sample.augment_mask
            if isinstance(aug_mask, torch.Tensor):
                aug_mask = aug_mask.detach().cpu().numpy()
        else:
            aug_mask = np.ones_like(gt_sem.detach().cpu().numpy())
            
        data = {
            'points': points,
            'imgs': imgs,
            'GT_label': {
                'gt_sem': gt_sem,
                'gt_inst': gt_inst,
            },
            'pseudo_label': UDA_pseudo_label,
            'sam': sam_results,
            'img_metas': {
                'lidar2img': lidar2img,
                'ori_shape': ori_shape,
                'scale_factor': scale_factor
            },
            'aug_mask': aug_mask
            
        }
        
        return data, device
            
    def update_data(self, unlabeled_data_copy, data, i, device):
        """
        更新单个frame的数据到unlabeled_data_copy
        
        Args:
            unlabeled_data_copy (dict): 原始未标记数据的副本
            data (dict): 增强后的数据
            i (int): 帧索引
            device (torch.device): 设备
            
        Returns:
            dict: 更新后的unlabeled_data_copy
        """
        # update points and imgs
        points = data['points']
        imgs = data['imgs']
        points = torch.from_numpy(points).float().to(device)
        img_tensors = [torch.from_numpy(img).float() for img in imgs]
        imgs = torch.stack(img_tensors, dim=0).to(device)
        unlabeled_data_copy['inputs']['points'][i] = points
        #TODO-YINING: here change 'img' to 'imgs'
        unlabeled_data_copy['inputs']['imgs'][i] = imgs
        
        # update pseudo_label & sam masks & aug_mask
        data_sample = unlabeled_data_copy['data_samples'][i]
        data_sample.cand_2d['sam_pmask_2d'] = data['sam']['sam_pmask']
        data_sample.cand_2d['sam_pscore_2d'] = data['sam']['sam_pscore']
        # print(data['pseudo_label'].keys(), data.keys())
        device = data['pseudo_label']['device']
        pts_semantic_mask = data['pseudo_label']['pts_semantic_mask']
        pts_instance_mask = data['pseudo_label']['pts_instance_mask']
        pts_sem_score = data['pseudo_label']['pts_sem_score']
        aug_mask = data['aug_mask']
        
        if device != 'numpy':
            pts_semantic_mask = torch.from_numpy(pts_semantic_mask).to(device)
            pts_instance_mask = torch.from_numpy(pts_instance_mask).to(device)
            pts_sem_score = torch.from_numpy(pts_sem_score).to(device)
            if aug_mask is not None:
                aug_mask = torch.from_numpy(aug_mask).to(device) 
            # if voxel_semantic_mask is not None:
            #     voxel_semantic_mask = torch.from_numpy(voxel_semantic_mask).to(device)
            # if voxel_instance_mask is not None:
            #     voxel_instance_mask = torch.from_numpy(voxel_instance_mask).to(device)        
        # if voxel_semantic_mask is not None:
        #     data_sample.set_data({'UDA_pseudo_label': PointData(**{'pts_semantic_mask': pts_semantic_mask, 
        #                                                        'pts_instance_mask': pts_instance_mask,
        #                                                        'pts_sem_score': pts_sem_score,
        #                                                        'voxel_semantic_mask': voxel_semantic_mask,
        #                                                        'voxel_instance_mask': voxel_instance_mask,
        #                                                        'point2voxel_map': point2voxel_map})})
        # else:
        data_sample.set_data({'UDA_pseudo_label': PointData(**{'pts_semantic_mask': pts_semantic_mask, 
                                                            'pts_instance_mask': pts_instance_mask,
                                                            'pts_sem_score': pts_sem_score})})
        data_sample.augment_mask = aug_mask
        unlabeled_data_copy['data_samples'][i] = data_sample
                
        return unlabeled_data_copy

    def set_datasample_mode(self, data_samples, mode, other_attrs=None):
        """设置data samples的模式和目标伪标签使用标志
        
        Args:
            data_samples (list): 数据样本列表
            mode (str): 模式设置，可选值为'teacher'或'student'
            other_attrs (dict): 其他需要设置的属性
            
        Returns:
            list: 更新后的数据样本列表
        """
        for data_sample in data_samples:
            data_sample.mt_mode = mode
            if other_attrs is not None:
                for key, value in other_attrs.items():
                    setattr(data_sample, key, value)
        return data_samples