"""
Superpoint processing utilities for point cloud clustering and segmentation.
"""

import numpy as np
import open3d as o3d
import hdbscan

import numpy as np
import open3d as o3d

def get_ground_mask(
        pts_xyz: np.ndarray,
        distance_threshold: float = 0.20,
        ransac_n: int = 3,
        num_iterations: int = 2000,
) -> np.ndarray:
    """
    使用 Open3D-RANSAC 拟合最大平面来提取地面点。

    Parameters
    ----------
    pts_xyz : (N, 3) or (N, ≥3) ndarray
        点云坐标。只会使用前 3 维的 (x, y, z)。
    distance_threshold : float, optional
        点到拟合平面的最大距离，超过则视为非地面。默认 0.20 m。
    ransac_n : int, optional
        每次 RANSAC 采样的点数。默认 3。
    num_iterations : int, optional
        RANSAC 迭代次数。默认 2000。

    Returns
    -------
    ground_mask : (N,) ndarray[bool]
        True 表示地面点，False 表示非地面点。
    """
    if pts_xyz.ndim != 2 or pts_xyz.shape[1] < 3:
        raise ValueError("pts_xyz must be an array of shape (N, 3) or (N, ≥3).")

    # 1. 构造 Open3D PointCloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts_xyz[:, :3])

    # 2. RANSAC 拟合地面平面
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=distance_threshold,
        ransac_n=ransac_n,
        num_iterations=num_iterations,
    )

    # 3. 生成布尔 mask
    ground_mask = np.zeros(len(pts_xyz), dtype=bool)
    ground_mask[inliers] = True
    return ground_mask


def tune_hdbscan_params(num_pts: int, is_ground: bool):
    """
    依据点数与地面/非地面属性动态设定 HDBSCAN 超参.
    Heuristic:   非地面 -> 小簇阈值; 地面 -> 大簇阈值.
    
    Args:
        num_pts (int): 点云中的点数
        is_ground (bool): 是否为地面点
        
    Returns:
        tuple: (min_cluster_size, min_samples)
    """
    if is_ground:
        # 地面点最多, 取 2% 点数但不低于 120 作为 min_cluster_size
        min_cluster_size = max(120, int(num_pts * 0.02))
        min_samples = max(25, int(num_pts * 0.003))
    else:
        # 物体实例: 0.5% 点数, 不低于 40
        min_cluster_size = max(40, int(num_pts * 0.005))
        min_samples = max(8, int(num_pts * 0.001))
    return min_cluster_size, min_samples


def process_RANSAC_HDB(points_xyz: np.ndarray,
                      voxel_size: float = 0.05,
                      dist_thresh: float = 0.20,
                      ground_mode: str = "single"):
    """
    使用RANSAC和HDBSCAN处理点云，生成几何实例标签
    
    Parameters
    ----------
    points_xyz : (N,3) float32/64
        输入点云坐标
    voxel_size : float
        体素边长 (m).
    dist_thresh : float
        RANSAC 平面阈值 (m).
    ground_mode : {"single", "cluster"}
        "single"  -> 所有 ground 设置为 -1;
        "cluster" -> 地面内部再跑一次 HDBSCAN, 生成细分 id.

    Returns
    -------
    instance_labels : (N,) int32
        -1 表 ground; 其他非负整数为实例 id.
    ground_mask : (N,) bool
        地面掩码
    """
    # 原始点云对象
    pcd_raw = o3d.geometry.PointCloud()
    pcd_raw.points = o3d.utility.Vector3dVector(points_xyz)

    # RANSAC 地面检测
    _, inliers = pcd_raw.segment_plane(distance_threshold=dist_thresh,
                                     ransac_n=3,
                                     num_iterations=2000)
    ground_mask = np.zeros(len(points_xyz), dtype=bool)
    ground_mask[inliers] = True

    # 体素下采样 + trace
    pc_down, trace_indices, _ = pcd_raw.voxel_down_sample_and_trace(
        voxel_size=voxel_size,
        min_bound=pcd_raw.get_min_bound(),
        max_bound=pcd_raw.get_max_bound())
    trace_indices = np.asarray(trace_indices, dtype=object)
    xyz_down = np.asarray(pc_down.points)

    # 根据每个体素选一个原始点来判 ground / non-ground 
    is_down_ground = np.array([ground_mask[ids[0]] for ids in trace_indices])
    non_ground_idx = np.where(~is_down_ground)[0]
    ground_idx = np.where(is_down_ground)[0]

    # 非地面聚类
    lbl_down = np.full(len(xyz_down), -1, dtype=np.int32)

    if non_ground_idx.size:
        mcs, ms = tune_hdbscan_params(non_ground_idx.size, is_ground=False)
        lbl_ng = hdbscan.HDBSCAN(min_cluster_size=mcs,
                               min_samples=ms).fit_predict(xyz_down[non_ground_idx])
        lbl_down[non_ground_idx] = lbl_ng

    # 地面聚类 / 统一设 -1
    if ground_idx.size:
        if ground_mode == "cluster":
            mcs_g, ms_g = tune_hdbscan_params(ground_idx.size, is_ground=True)
            lbl_g = hdbscan.HDBSCAN(min_cluster_size=mcs_g,
                                  min_samples=ms_g).fit_predict(xyz_down[ground_idx])
            # 避免 id 混冲: 全局最大 id + 1 偏移
            offset = lbl_down.max() + 1
            lbl_g = np.where(lbl_g == -1, -1, lbl_g + offset)
            lbl_down[ground_idx] = lbl_g     # -1 或细分 id
        else:
            lbl_down[ground_idx] = -1         # 整体 ground

    # 回填到原始大小
    instance_labels = np.full(len(points_xyz), -1, dtype=np.int32)
    for down_i, orig_ids in enumerate(trace_indices):
        valid_ids = orig_ids[orig_ids >= 0]
        if valid_ids.size:
            valid_ids = valid_ids.astype(np.int64)
            instance_labels[valid_ids] = lbl_down[down_i]

    return instance_labels.astype(np.int32), ground_mask 