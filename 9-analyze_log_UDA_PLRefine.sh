# UNDER OLD SETTINGS
LOG_FILE='work_dirs/IAL_MT_city_syncBuffer_PLgrowSGth_XshareQ_Xconsist_HPC4/20250721_085856/vis_data/20250721_085856.json'
LOG_FILE2='work_dirs/IAL_MT_city_syncBuffer_PLgrowSGst_XshareQ_Xconsist_HPC3/20250721_021001/vis_data/20250721_021001.json'
SAVE_PATH='output/plot_curve/UDA'
SAVE_NAME="grow_th_st"
SAVE_FILE="$SAVE_PATH/$SAVE_NAME"
key_1 = "pq" "rq_mean" "miou_dagger" "sq_mean" "miou"
key_2 = "loss"

for metric in "pq" "miou" "loss" "pq_dagger" "sq_mean" "rq_mean" "pq_things" "rq_things" "sq_things" "pq_stuff" "rq_stuff" "sq_stuff" 
do 
    echo "*** Plotting loss curve... ***"
    python tools/analysis_tools/analyze_logs.py \
        plot_curve \
        $LOG_FILE $LOG_FILE2 \
        --keys $metric \
        --title "$SAVE_NAME $metric" \
        --out "$SAVE_FILE _$metric.png" \
        --legend "PLRefine_th" "PLRefine_st" \
        # --eval \
done
