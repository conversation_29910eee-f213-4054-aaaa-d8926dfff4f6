

import os
import sys
# sys.path.append(os.path.join(os.getcwd(), "/.."))
import numpy as np
import torch
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS
from typing import List, Optional, Sequence, Tuple, Union



import numpy as np
import torch
from typing import List, Tuple

from nuscenes.nuscenes import NuScenes, NuScenesExplorer
from nuscenes.utils.data_classes import LidarPointCloud
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion
from nuscenes.utils.geometry_utils import view_points

import os.path as osp
from PIL import Image
    
def map_pointcloud_to_image_mask(nusc,
                            points_data: np.ndarray,  # (4, N)
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
    
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
        

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    


def view_points(points: np.ndarray, view: np.ndarray, normalize: bool) -> np.ndarray:
    """
    This is a helper class that maps 3d points to a 2d plane. It can be used to implement both perspective and
    orthographic projections. It first applies the dot product between the points and the view. By convention,
    the view should be such that the data is projected onto the first 2 axis. It then optionally applies a
    normalization along the third dimension.

    For a perspective projection the view should be a 3x3 camera matrix, and normalize=True
    For an orthographic projection with translation the view is a 3x4 matrix and normalize=False
    For an orthographic projection without translation the view is a 3x3 matrix (optionally 3x4 with last columns
     all zeros) and normalize=False

    :param points: <np.float32: 3, n> Matrix of points, where each point (x, y, z) is along each column.
    :param view: <np.float32: n, n>. Defines an arbitrary projection (n <= 4).
        The projection should be such that the corners are projected onto the first 2 axis.
    :param normalize: Whether to normalize the remaining coordinate (along the third axis).
    :return: <np.float32: 3, n>. Mapped point. If normalize=False, the third coordinate is the height.
    """

    assert view.shape[0] <= 4
    assert view.shape[1] <= 4
    assert points.shape[0] == 3

    viewpad = np.eye(4)
    viewpad[:view.shape[0], :view.shape[1]] = view

    nbr_points = points.shape[1]

    # Do operation in homogenous coordinates.
    points = np.concatenate((points, np.ones((1, nbr_points))))
    points = np.dot(viewpad, points)
    points = points[:3, :]

    if normalize:
        points = points / points[2:3, :].repeat(3, 0).reshape(3, nbr_points)
        # print(f'************** div {np.unique(points[2:3, :].repeat(3, 0).reshape(3, nbr_points))} **************')

    return points

import numpy as np
import torch
from typing import List, Tuple

from nuscenes.nuscenes import NuScenes, NuScenesExplorer
from nuscenes.utils.data_classes import LidarPointCloud
from pyquaternion import Quaternion
from nuscenes.utils.geometry_utils import view_points

import os.path as osp
from PIL import Image
    
def map_pointcloud_to_image_mask_aug(nusc,
                            points_data: np.ndarray,  # (4, N)
                            aug_mat: np.ndarray,
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
    print(f'************** points_data {pc.points} **************')
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    
    # Init step: augment transformation: rotate, scale, translate
    aug_mat_inv = np.linalg.inv(aug_mat)    
    # pc.points = aug_mat_inv.T @ pc.points # R^T @ P^T, (4, 4) @ (4, N)  
    print(f'************** aug_mat {aug_mat} **************')
    print(f'************** aug_mat_inv {aug_mat_inv} **************')
    pc.rotate(aug_mat_inv[:3, :3].T)
    
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
    
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
        

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    

def plot_point_in_camview(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[0, :], points[1, :], c=coloring, s=dot_size)
    ax.axis('off')
    return fig, ax

def save_render(points, coloring, im, dot_size=5, save_path='misc/intermedia_data/render_result.png'):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[0, :], points[1, :], c=coloring, s=dot_size)
    # ax.axis('off')
    plt.savefig(save_path)
    return fig, ax

def gen_block_mask(points):
    upper_points, lower_points = sliding_window_max_min(points, window_size)
    
    if upper_points.shape[0] == 0 or lower_points.shape[0] == 0:
        return np.zeros((900, 1600))
    # 4. 分别拟合上界和下界
    coeffs_upper = np.polyfit(upper_points[:, 0], upper_points[:, 1], deg=2)
    coeffs_lower = np.polyfit(lower_points[:, 0], lower_points[:, 1], deg=2)

    # 生成拟合曲线
    x_fit = np.linspace(0, 1600, 1600)
    y_fit_upper = np.polyval(coeffs_upper, x_fit)
    y_fit_lower = np.polyval(coeffs_lower, x_fit)

    # 5. 生成mask
    mask = np.zeros((900, 1600))
    for i, x in enumerate(x_img):
        # 确保 y 值在图像范围内
        y_upper = int(np.clip(y_fit_upper[i], 0, image_shape[0] - 1))
        y_lower = int(np.clip(y_fit_lower[i], 0, image_shape[0] - 1))

        # 2. 将位于 [y_lower, y_upper] 范围内的像素值设为 1
        mask[y_lower:y_upper, int(x)] = 1
    # for i in range(1600):
    #     mask[i, int(y_fit_lower[i]):int(y_fit_upper[i])] = 1
    return mask


from nuscenes.nuscenes import NuScenes
# from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion
import numpy as np

# dataroot = os.getcwd()+'/data/nuscenes_full'
# nusc = NuScenes(version='v1.0-trainval', dataroot=dataroot, verbose=True)

sample_token_v = '0a8a0fa9682c464d93ff345d36e7515f'

sample_v = nusc.get('sample_data', sample_token_v)
sample_token = sample_v['sample_token']
pointsensor_token_v = nusc.get('sample', sample_token) # sample_v['data']['LIDAR_TOP'])
# point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
# cam_token_v = sample_v['data']['CAM_FRONT']


# sample_v
pointsensor_token_v


voxel_coor_torch = torch.load('misc/intermedia_data/voxel_coordinates.pt')
voxel_coor_np = np.load('misc/intermedia_data/voxel_coordinates.npy')
point_coor_torch = torch.load('misc/intermedia_data/point_coordinates.pt')

voxel_idx_coor = voxel_coor_torch.cpu().numpy()
voxel_idx_coor = voxel_idx_coor[:, 1:] # remove batch idx
point_coor = point_coor_torch.cpu().numpy()
# voxel_coor = np.concatenate((voxel_coor[:, 1:], np.ones((voxel_coor.shape[0], 1))), axis=1)
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)

# point_coor_v = input_dict['points'].tensor
# point_coor_v = point_coor_v.numpy()
# point_coor_v = np.concatenate((point_coor, np.ones((point_coor_v.shape[0], 1))), axis=1)



point_coor_v = point_coor
point_cam_coords, coloring_p, im_p, mask_p = map_pointcloud_to_image_mask(nusc, point_coor_v, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords[:, mask_p], coloring_p[mask_p], im_p, dot_size=5)

np.linalg.inv(R)

# rotation

# R = np.array([[0.0000000, -1.0000000, 0.0000000],
#                 [1.0000000, 0.0000000, 0.0000000],
#                 [0.0000000, 0.0000000, 1.0000000]])

# R2, sin(30), cos(30)
# R = np.array([[0.8660254, -0.5000000, 0.0000000],
#                 [0.5000000, 0.8660254, 0.0000000],
#                 [0.0000000, 0.0000000, 1.0000000]])

# R3, sin(0), cos(0)
R = np.array([[0.0000000, -0.0000000, 1.0000000],
                [0.0000000, 1.0000000, 0.0000000],
                [-1.0000000, 0.0000000, 0.0000000]])


R_pad = np.eye(4)
R_pad[:R.shape[0], :R.shape[1]] = R
M = np.eye(4)

point_coor_v_rot = point_coor @ R_pad
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(nusc, point_coor_v_rot, R_pad, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

S = np.eye(4)
scale = 0.3
S = S * scale

point_coor_v_rot = point_coor @ S
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(nusc, point_coor_v_rot, S, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

T = np.eye(4)
T[0, 3] = 100
T[1, 3] = -100000000
T[2, 3] = -100000000


point_coor_v_rot = point_coor @ M
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(
    nusc, point_coor_v_rot, T, 
    point_calibrated_sensor_token_v, 
    point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

# from numpy import random

# resize_lim = (0.95*0.001, 1.05*0.001)
# rot_lim = (-0.78539816*10, 0.78539816*100)
# trans_lim = 0.2

# scale = random.uniform(low=resize_lim[0], high=resize_lim[1])
# theta = random.uniform(rot_lim)
# translation = np.array([random.normal(0, trans_lim) for i in range(3)])
# rotation = np.eye(3)

# transform = np.eye(4).astype(np.float32)
# transform[:3, :3] = rotation.T * scale
# transform[:3, 3] = translation * scale
transform = np.eye(4)
transform[:3, :3] = R
transform[:3, 3] = np.array([100, 100, 100])
transform = transform*scale

R_pad = transform
M = np.eye(4)
point_coor_v_rot = point_coor @ R_pad
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(
    nusc, point_coor_v_rot, R_pad, 
    point_calibrated_sensor_token_v, 
    point_ego_pose_token_v, 
    cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

rotation = np.eye(3) # should be previous r+s+t transform matrix
rotation = np.array([[1, 0, 0], [0, -1, 0], [0, 0, 1]]) @ rotation # vertical, flip y axis
# rotation = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, 1]]) @ rotation # horizontal, flip x axis

R_pad = np.eye(4)
R_pad[:rotation.shape[0], :rotation.shape[1]] = rotation

point_coor_v_rot = point_coor @ R_pad
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(nusc, point_coor_v_rot, R_pad, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

import h5py
import numpy as np
import os
import sys
import pickle
# from tools.general_tool import load_point, load_mask, load_viewimages_meta, load_viewimages

device='HPC1'
if device == 'HPC5':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'HPC3':
    proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
elif device == 'HPC1':
    proj_path='/mnt/data/data/yining/codefield/3dmmpano_240109/'
elif device == 'HPC4':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
else:
    proj_path = '.'
    
sys.path.append(proj_path)

pklfile = os.path.join(proj_path, 'data/nuscenes_full/nuscenes_infos_train.pkl')


from nuscenes.nuscenes import NuScenes, NuScenesExplorer

dataroot = os.path.join(proj_path, 'data/nuscenes_mini')
nusc = NuScenes(version='v1.0-mini', dataroot=dataroot, verbose=True)


sample_token_v = 'ca9a282c9e77460f8360f564131a8af5'

sample_v = nusc.get('sample', sample_token_v)
pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
cam_token_v = sample_v['data']['CAM_FRONT']

with open(pklfile, 'rb') as f:
    data = pickle.load(f)
    
# view = data['data_list'][0]['images']['CAM_FRONT']
# lidar2cam = view['lidar2cam']
# cam2img = view['cam2img']



data['data_list'][0]['images']['CAM_FRONT'].keys()

info = data['data_list'][0]
VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
data_prefix = {
    'dataset_path': 'data/nuscenes_mini',
    'lidar_path': 'data/nuscenes_mini/samples/LIDAR_TOP',
    'pts_panoptic_mask': 'data/nuscenes_mini/panoptic/v1.0-mini',
    'CAM_FRONT': 'data/nuscenes_mini/samples/CAM_FRONT',
    'CAM_FRONT_RIGHT': 'data/nuscenes_mini/samples/CAM_FRONT_RIGHT',
    'CAM_FRONT_LEFT': 'data/nuscenes_mini/samples/CAM_FRONT_LEFT',
    'CAM_BACK': 'data/nuscenes_mini/samples/CAM_BACK',
    'CAM_BACK_LEFT': 'data/nuscenes_mini/samples/CAM_BACK_LEFT',
    'CAM_BACK_RIGHT': 'data/nuscenes_mini/samples/CAM_BACK_RIGHT',}

img_files_path = {}
meta_info = {}
for cam_id, img_info in info['images'].items(): # add prefix to img_path
    print(cam_id, img_info)
    if 'img_path' in img_info:
        if cam_id in data_prefix:
            cam_prefix = data_prefix[cam_id]
    #     else:
    #         cam_prefix = data_prefix.get('img', '')
        img_info['img_path'] = os.path.join(cam_prefix,
                                        img_info['img_path'])
        img_files_path[cam_id] = img_info['img_path']
    meta_info[cam_id] = {}
    if 'lidar2cam' in img_info:
        meta_info[cam_id]['lidar2cam'] = np.array(img_info['lidar2cam'])
    if 'cam2img' in img_info:
        meta_info[cam_id]['cam2img'] = np.array(img_info['cam2img'])


imgs = load_viewimages(img_files_path)


    
def map_pointcloud_to_image_2(nusc,
                            points_data: np.ndarray,  # (4, N)
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False,
                            aug_mat: np.ndarray = None,
                            sem_mask: np.ndarray = None,
                            use_label_map: bool = False
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    if sem_mask is not None:
        if np.unique(sem_mask).shape[0]<=2:
            color_map = {
                0: [173, 197, 207], # light lake blue
                1: [41, 96, 115], # lake blue
            }
        else:
            color_map = {
                0: [0, 0, 0],  # noise                 black
                1: [255, 120, 50],  # barrier               orange
                2: [255, 192, 203],  # bicycle               pink
                3: [255, 255, 0],  # bus                   yellow
                4: [0, 150, 245],  # car                   blue
                5: [0, 255, 255],  # construction_vehicle  cyan
                6: [255, 127, 0],  # motorcycle            dark orange
                7: [255, 0, 0],  # pedestrian            red
                8: [255, 240, 150],  # traffic_cone          light yellow
                9: [135, 60, 0],  # trailer               brown
                10: [160, 32, 240],  # truck                 purple
                11: [255, 0, 255],  # driveable_surface     dark pink
                12: [139, 137, 137],  # other_flat            dark red
                13: [75, 0, 75],  # sidewalk              dark purple
                14: [150, 240, 80],  # terrain               light green
                15: [230, 230, 250],  # manmade               white
                16: [0, 175, 0],  # vegetation            green
            }   
        label_map = {
            1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
            9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
            22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
            }
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
    # print(f'************** points_data {pc.points} **************')
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    
    # Init step: augment transformation: rotate, scale, translate
    if aug_mat is not None:
        aug_mat_inv = np.linalg.inv(aug_mat)    
        # pc.points = aug_mat_inv.T @ pc.points # R^T @ P^T, (4, 4) @ (4, N)  
        # print(f'************** aug_mat {aug_mat} **************')
        # print(f'************** aug_mat_inv {aug_mat_inv} **************')
        
        #TODO: seems already done inverse transformation, need to check
        # pc.rotate(aug_mat_inv[:3, :3].T)
        pc.rotate(aug_mat[:3, :3].T)
        
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
        
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        if sem_mask is not None:
            coloring = sem_mask
            # map semantic label to color
            if use_label_map:
                coloring = np.array([color_map[label_map[i]] for i in coloring])
            else:
                coloring = np.array([color_map[i] for i in coloring])
                # print(f'************** coloring **************')
            
            coloring = coloring/255
        else:
            coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
        

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    


data_root = 'data/nuscenes_mini'

def load_point_mask_viewimages(pklfile, data_root):
    label_map = {
            1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
            9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
            22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
            }
    
    info_list = pklfile['data_list']
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',}

    res_list = []
    for info in info_list:
        res = {}
        
        # load lidar points
        lidar_prefix = data_prefix.get('lidar_path')
        lidar_path = info['lidar_points']['lidar_path']
        lidar_path = os.path.join(lidar_prefix, lidar_path)
        lidar_point = load_point(lidar_path)
        res['lidar_point'] = lidar_point
        
        # load panoptic mask
        dataset_path = data_prefix.get('dataset_path')
        panoptic_path = info['pts_panoptic_mask_path']
        panoptic_path = os.path.join(dataset_path, panoptic_path)
        pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
        # map to 0-19 classes
        pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
        res['pts_semantic_mask'] = pts_semantic_mask
        res['pts_instance_mask'] = pts_instance_mask
        
        # load multi-view images
        img_files_path = {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            if 'img_path' in img_info:
                if cam_id in data_prefix:
                    cam_prefix = data_prefix[cam_id]
                img_info['img_path'] = os.path.join(cam_prefix,
                                                img_info['img_path'])
                img_files_path[cam_id] = img_info['img_path']
        imgs = load_viewimages(img_files_path)
        
        # pack by view
        meta_info = {}
        meta_info['img'] = {}
        
        for view in VIEWS:
            meta_info['img'][view] = imgs[view]
            
        # load multi-view images meta
        meta_info['lidar2cam'], meta_info['cam2img'] = {}, {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            # meta_info[cam_id] = {}
            if 'lidar2cam' in img_info:
                meta_info['lidar2cam'][cam_id] = np.array(img_info['lidar2cam'])
            if 'cam2img' in img_info:
                meta_info['cam2img'][cam_id] = np.array(img_info['cam2img'])

        res['imgs_meta'] = meta_info
        res_list.append(res)
    return res_list

pklfile = os.path.join(proj_path, 'data/nuscenes_mini/nuscenes_infos_train.pkl')

with open(pklfile, 'rb') as f:
    data = pickle.load(f)

res_list = load_point_mask_viewimages(data, data_root)

color_map = {
    0: [0, 0, 0],  # noise                 black
    1: [255, 120, 50],  # barrier               orange
    2: [255, 192, 203],  # bicycle               pink
    3: [255, 255, 0],  # bus                   yellow
    4: [0, 150, 245],  # car                   blue
    5: [0, 255, 255],  # construction_vehicle  cyan
    6: [255, 127, 0],  # motorcycle            dark orange
    7: [255, 0, 0],  # pedestrian            red
    8: [255, 240, 150],  # traffic_cone          light yellow
    9: [135, 60, 0],  # trailer               brown
    10: [160, 32, 240],  # truck                 purple
    11: [255, 0, 255],  # driveable_surface     dark pink
    12: [139, 137, 137],  # other_flat            dark red
    13: [75, 0, 75],  # sidewalk              dark purple
    14: [150, 240, 80],  # terrain               light green
    15: [230, 230, 250],  # manmade               white
    16: [0, 175, 0],  # vegetation            green
}   

label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16

}

from typing import List, Tuple, Union

def map_pointcloud_to_image_mask_aug_semantic(nusc,
                            points_data: np.ndarray,  # (4, N)
                            aug_mat: np.ndarray,
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False,
                            sem_mask: np.ndarray = None
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    if sem_mask is not None:
        color_map = {
            0: [0, 0, 0],  # noise                 black
            1: [255, 120, 50],  # barrier               orange
            2: [255, 192, 203],  # bicycle               pink
            3: [255, 255, 0],  # bus                   yellow
            4: [0, 150, 245],  # car                   blue
            5: [0, 255, 255],  # construction_vehicle  cyan
            6: [255, 127, 0],  # motorcycle            dark orange
            7: [255, 0, 0],  # pedestrian            red
            8: [255, 240, 150],  # traffic_cone          light yellow
            9: [135, 60, 0],  # trailer               brown
            10: [160, 32, 240],  # truck                 purple
            11: [255, 0, 255],  # driveable_surface     dark pink
            12: [139, 137, 137],  # other_flat            dark red
            13: [75, 0, 75],  # sidewalk              dark purple
            14: [150, 240, 80],  # terrain               light green
            15: [230, 230, 250],  # manmade               white
            16: [0, 175, 0],  # vegetation            green
        }   
        label_map = {
            1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
            9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
            22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
            }
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
    print(f'************** points_data {pc.points} **************')
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    
    # Init step: augment transformation: rotate, scale, translate
    aug_mat_inv = np.linalg.inv(aug_mat)    
    # pc.points = aug_mat_inv.T @ pc.points # R^T @ P^T, (4, 4) @ (4, N)  
    print(f'************** aug_mat {aug_mat} **************')
    print(f'************** aug_mat_inv {aug_mat_inv} **************')
    pc.rotate(aug_mat_inv[:3, :3].T)
    
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
    
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        if sem_mask is not None:
            coloring = sem_mask
            # map semantic label to color
            coloring = np.array([color_map[label_map[i]] for i in coloring])
            coloring = coloring/255
        else:
            coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
        

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    

from tools.projection.pc2img import map_pointcloud_to_image_2


# resize image PIL
def resize_img(img, size=(200, 200)):
    img = img.resize(size)
    return img



### rotation
R = np.array([[0.0000000, -0.0000000, 1.0000000],
                [0.0000000, 1.0000000, 0.0000000],
                [-1.0000000, 0.0000000, 0.0000000]])
R_pad = np.eye(4)
R_pad[:R.shape[0], :R.shape[1]] = R
M = np.eye(4)

### load point cloud from pkl file
i = 0
point_coor = res_list[i]['lidar_point'].copy()
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)
point_coor = point_coor @ R_pad

### load semantic mask
sem_mask = res_list[i]['pts_semantic_mask']

### map point cloud to image
point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
    nusc, point_coor, 
    point_calibrated_sensor_token_v, point_ego_pose_token_v, 
    cam_token_v, min_dist=1.0, return_img=True,
    sem_mask=sem_mask,
    aug_mat=np.linalg.inv(R_pad)
    )


coloring_p.min(), coloring_p.max()


plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p], im_p, dot_size=5)

plot_point_in_camview(point_cam_coords1[:, mask_p]*0.4, coloring_p[mask_p], resize_img(im_p, size=(640,360)), dot_size=5)

# save_render(point_cam_coords1[:, mask_p], coloring_p[mask_p], im_p, dot_size=5, save_path='misc/intermedia_data/pointcloud_camview_semantic.png')

from mmdet3d.datasets import GlobalRotScaleTrans

# @TRANSFORMS.register_module()
class GlobalRotScaleTrans_MM(GlobalRotScaleTrans):
    """Compared with `GlobalRotScaleTrans`, the augmentation order in this
    class is rotation, translation and scaling (RTS)."""
    
    def _rot_bbox_points(self, input_dict: dict, axis: int) -> None:
        """Private function to rotate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after rotation, 'points', 'pcd_rotation'
            and `gt_bboxes_3d` is updated in the result dict.
        """
        rotation = self.rot_range
        noise_rotation = np.random.uniform(rotation[0], rotation[1])

        if 'gt_bboxes_3d' in input_dict and \
                len(input_dict['gt_bboxes_3d'].tensor) != 0:
            # rotate points with bboxes
            points, rot_mat_T = input_dict['gt_bboxes_3d'].rotate(
                noise_rotation, input_dict['points'], axis=axis)
            input_dict['points'] = points
        else:
            # if no bbox in input_dict, only rotate points
            rot_mat_T = input_dict['points'].rotate(noise_rotation, axis=axis)

        input_dict['pcd_rotation'] = rot_mat_T
        input_dict['pcd_rotation_angle'] = noise_rotation

    def transform(self, input_dict: dict) -> dict:
        """Private function to rotate, scale and translate bounding boxes and
        points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points', 'pcd_rotation',
            'pcd_scale_factor', 'pcd_trans' and `gt_bboxes_3d` are updated
            in the result dict.
        """
        # if 'transformation_3d_flow' not in input_dict:
        #     input_dict['transformation_3d_flow'] = []
        rot_axis = input_dict.get('rot_axis', 2)
        self._rot_bbox_points(input_dict, axis=rot_axis)

        if 'pcd_scale_factor' not in input_dict:
            self._random_scale(input_dict)
        self._trans_bbox_points(input_dict)
        self._scale_bbox_points(input_dict)

        # input_dict['transformation_3d_flow'].extend(['R', 'T', 'S'])

        lidar_augs = np.eye(4)
        # scale = input_dict['pcd_scale_factor']
        # scale_mat = torch.tensor(
        #     [
        #         [scale, 0, 0, 0],
        #         [0, scale, 0, 0],
        #         [0, 0, scale, 0],
        #         [0, 0, 0, 1],
        #     ]
        # )
        
        print(f'pcd_rotation is: ', input_dict['pcd_rotation'])
        print('scale is: ', input_dict['pcd_scale_factor'])
        print('pcd_trans is: ', input_dict['pcd_trans'])
        # lidar_augs[:3, :3] *= input_dict['pcd_scale_factor']
        lidar_augs[:3, :3] = input_dict['pcd_rotation'].T \
            * input_dict['pcd_scale_factor']
        lidar_augs[:3, 3] = input_dict['pcd_trans'] * input_dict['pcd_scale_factor']

        print(f'lidar_augs', lidar_augs)
        if 'lidar_aug_matrix' not in input_dict:
            input_dict['lidar_aug_matrix'] = np.eye(4)
        input_dict[
            'lidar_aug_matrix'] = lidar_augs @ input_dict['lidar_aug_matrix']

        return input_dict



# @PIPELINES.register_module()
class GlobalRotScaleTransAll(object):
    """Modify: 
    1. rotate along z axis, previous rotate along x axis.
    2. save lidar2cam, lidar2img as torch.Tensor.
    """
    """Apply global rotation, scaling and translation to a 3D scene.

    Args:
        rot_range (list[float]): Range of rotation angle.
            Defaults to [-0.78539816, 0.78539816] (close to [-pi/4, pi/4]).
        scale_ratio_range (list[float]): Range of scale ratio.
            Defaults to [0.95, 1.05].
        translation_std (list[float]): The standard deviation of translation
            noise. This applies random translation to a scene by a noise, which
            is sampled from a gaussian distribution whose standard deviation
            is set by ``translation_std``. Defaults to [0, 0, 0]
        shift_height (bool): Whether to shift height.
            (the fourth dimension of indoor points) when scaling.
            Defaults to False.
    """

    def __init__(self,
                 rot_range=[-0.78539816, 0.78539816],
                 scale_ratio_range=[0.95, 1.05],
                 translation_std=[0, 0, 0],
                 shift_height=False):
        seq_types = (list, tuple, np.ndarray)
        if not isinstance(rot_range, seq_types):
            assert isinstance(rot_range, (int, float)), \
                f'unsupported rot_range type {type(rot_range)}'
            rot_range = [-rot_range, rot_range]
        self.rot_range = rot_range

        assert isinstance(scale_ratio_range, seq_types), \
            f'unsupported scale_ratio_range type {type(scale_ratio_range)}'
        self.scale_ratio_range = scale_ratio_range

        if not isinstance(translation_std, seq_types):
            assert isinstance(translation_std, (int, float)), \
                f'unsupported translation_std type {type(translation_std)}'
            translation_std = [
                translation_std, translation_std, translation_std
            ]
        assert all([std >= 0 for std in translation_std]), \
            'translation_std should be positive'
        self.translation_std = translation_std
        self.shift_height = shift_height

    def _trans_bbox_points(self, input_dict):
        """Private function to translate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after translation, 'points', 'pcd_trans' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        translation_std = np.array(self.translation_std, dtype=np.float32)
        trans_factor = np.random.normal(scale=translation_std, size=3).T
        
        input_dict['points'].translate(trans_factor)
        if 'radar' in input_dict:
            input_dict['radar'].translate(trans_factor)
        input_dict['pcd_trans'] = trans_factor
        for key in input_dict['bbox3d_fields']:
            input_dict[key].translate(trans_factor)

        trans_mat = torch.eye(4)
        trans_factor = torch.tensor(trans_factor)
        trans_mat[:3, -1] = trans_factor
        trans_mat_inv = torch.linalg.inv(trans_mat)
        
        if 'lidar_aug_matrix' not in input_dict:
            input_dict['lidar_aug_matrix'] = torch.eye(4)
        input_dict['lidar_aug_matrix'] = trans_mat @ input_dict['lidar_aug_matrix']
        for view in input_dict["lidar2img"].keys():
            input_dict["lidar2img"][view] = input_dict["lidar2img"][view] @ trans_mat_inv
            input_dict["lidar2cam"][view] = input_dict["lidar2cam"][view] @ trans_mat_inv

    def _rot_bbox_points(self, input_dict, axis):
        """Private function to rotate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after rotation, 'points', 'pcd_rotation' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        if 'rot_degree' not in input_dict:
            rotation = self.rot_range
            noise_rotation = np.random.uniform(rotation[0], rotation[1])
        else:
            noise_rotation = input_dict['rot_degree']

        # if no bbox in input_dict, only rotate points
        if len(input_dict['bbox3d_fields']) == 0:
            if 'rot_degree' not in input_dict:
                rot_mat_T = input_dict['points'].rotate(noise_rotation, axis=axis)
                if 'radar' in input_dict:
                    input_dict['radar'].rotate(noise_rotation, axis=axis)
            else:
                rot_mat_T = input_dict['points'].rotate(-noise_rotation, axis=axis)
                if 'radar' in input_dict:
                    input_dict['radar'].rotate(-noise_rotation, axis=axis)
            input_dict['pcd_rotation'] = rot_mat_T

            rot_mat = torch.eye(4)
            rot_mat[:3, :3].copy_(rot_mat_T.T)
            print(f'************** rot_mat_T {rot_mat_T} **************')
            # rot_mat[0, 1], rot_mat[1, 0] = -rot_mat[0, 1], -rot_mat[1, 0] # rot_mat_T is inversed (transposed) rotate mat
            rot_mat_inv = torch.inverse(rot_mat)
            if 'lidar_aug_matrix' not in input_dict:
                input_dict['lidar_aug_matrix'] = torch.eye(4)
            input_dict['lidar_aug_matrix'] = rot_mat @ input_dict['lidar_aug_matrix']

            for view in input_dict["lidar2img"].keys():
                input_dict["lidar2img"][view] = torch.tensor(input_dict["lidar2img"][view]).float() @ rot_mat_inv
                input_dict["lidar2cam"][view] = torch.tensor(input_dict["lidar2cam"][view]).float() @ rot_mat_inv
            return

        ## not support rotate bboxes yet
        # # rotate points with bboxes
        # for key in input_dict['bbox3d_fields']:
        #     if len(input_dict[key].tensor) != 0:
        #         points, rot_mat_T = input_dict[key].rotate(
        #             noise_rotation, input_dict['points'])
        #         input_dict['points'] = points
        #         input_dict['pcd_rotation'] = rot_mat_T
        #         if 'radar' in input_dict:
        #             input_dict['radar'].rotate(-noise_rotation)

        #         rot_mat = torch.eye(4)
        #         rot_mat[:3, :3].copy_(rot_mat_T)
        #         rot_mat[0, 1], rot_mat[1, 0] = -rot_mat[0, 1], -rot_mat[1, 0] # rot_mat_T is inversed (transposed) rotate mat
        #         rot_mat_inv = torch.inverse(rot_mat)
        #         for view in input_dict["lidar2img"].keys():
        #             input_dict["lidar2img"][view] = torch.tensor(input_dict["lidar2img"][view]).float() @ rot_mat_inv
        #             input_dict["lidar2cam"][view] = torch.tensor(input_dict["lidar2cam"][view]).float() @ rot_mat_inv


    def _scale_bbox_points(self, input_dict):
        """Private function to scale bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points'and keys in \
                input_dict['bbox3d_fields'] are updated in the result dict.
        """
        scale = input_dict['pcd_scale_factor']
        points = input_dict['points']
        points.scale(scale)
        if self.shift_height:
            assert 'height' in points.attribute_dims.keys(), \
                'setting shift_height=True but points have no height attribute'
            points.tensor[:, points.attribute_dims['height']] *= scale
        input_dict['points'] = points
        
        if 'radar' in input_dict:
            input_dict['radar'].scale(scale)
            
        for key in input_dict['bbox3d_fields']:
            input_dict[key].scale(scale)

        scale_mat = torch.tensor(
            [
                [scale, 0, 0, 0],
                [0, scale, 0, 0],
                [0, 0, scale, 0],
                [0, 0, 0, 1],
            ], dtype=torch.float32
        )
        scale_mat_inv = torch.inverse(scale_mat)
        if 'lidar_aug_matrix' not in input_dict:
            input_dict['lidar_aug_matrix'] = torch.eye(4)
        input_dict['lidar_aug_matrix'] = scale_mat @ input_dict['lidar_aug_matrix']

        for view in input_dict["lidar2img"].keys():
            input_dict["lidar2img"][view] = torch.tensor(input_dict["lidar2img"][view]).float() @ scale_mat_inv
            input_dict["lidar2cam"][view] = torch.tensor(input_dict["lidar2cam"][view]).float() @ scale_mat_inv

    def _random_scale(self, input_dict):
        """Private function to randomly set the scale factor.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'pcd_scale_factor' are updated \
                in the result dict.
        """
        scale_factor = np.random.uniform(self.scale_ratio_range[0],
                                         self.scale_ratio_range[1])
        input_dict['pcd_scale_factor'] = scale_factor

    def __call__(self, input_dict, rot_axis=2):
        """Private function to rotate, scale and translate bounding boxes and \
        points.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            rot_axis (int): Rotation axis. Defaults to z-axis.

        Returns:
            dict: Results after scaling, 'points', 'pcd_rotation',
                'pcd_scale_factor', 'pcd_trans' and keys in \
                input_dict['bbox3d_fields'] are updated in the result dict.
        """
        if 'transformation_3d_flow' not in input_dict:
            input_dict['transformation_3d_flow'] = []

        self._rot_bbox_points(input_dict, axis=rot_axis)

        if 'pcd_scale_factor' not in input_dict:
            self._random_scale(input_dict)
        self._scale_bbox_points(input_dict)

        self._trans_bbox_points(input_dict)

        input_dict['transformation_3d_flow'].extend(['R', 'S', 'T'])
        return input_dict

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(rot_range={self.rot_range},'
        repr_str += f' scale_ratio_range={self.scale_ratio_range},'
        repr_str += f' translation_std={self.translation_std},'
        repr_str += f' shift_height={self.shift_height})'
        return repr_str



from typing import Any, Dict

class RandomFlip3D_MM:
    """Compared with `RandomFlip3D`, this class directly records the lidar
    augmentation matrix in the `data`."""

    def __call__(self, data: Dict[str, Any]) -> Dict[str, Any]:
        flip_horizontal = np.random.choice([0, 1])
        flip_vertical = np.random.choice([0, 1])

        rotation = np.eye(3)
        if flip_horizontal:
            rotation = np.array([[1, 0, 0], [0, -1, 0], [0, 0, 1]]) @ rotation
            if 'points' in data:
                data['points'].flip('horizontal')
            if 'gt_bboxes_3d' in data:
                data['gt_bboxes_3d'].flip('horizontal')
            if 'gt_masks_bev' in data:
                data['gt_masks_bev'] = data['gt_masks_bev'][:, :, ::-1].copy()

        if flip_vertical:
            rotation = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, 1]]) @ rotation
            if 'points' in data:
                data['points'].flip('vertical')
            if 'gt_bboxes_3d' in data:
                data['gt_bboxes_3d'].flip('vertical')
            if 'gt_masks_bev' in data:
                data['gt_masks_bev'] = data['gt_masks_bev'][:, ::-1, :].copy()

        if 'lidar_aug_matrix' not in data:
            data['lidar_aug_matrix'] = np.eye(4)
        data['lidar_aug_matrix'][:3, :] = rotation @ data[
            'lidar_aug_matrix'][:3, :]
        return data


### load point cloud from pkl file
point_coor = out_dict['points'].copy()
# i = 0
# point_coor = res_list[i]['lidar_point'].copy()
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)

### rotation
R = out_dict['lidar_aug_matrix']
point_coor = point_coor @ R

### load semantic mask
sem_mask = res_list[i]['pts_semantic_mask']

### map point cloud to image
point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_mask_aug_semantic(
    nusc, point_coor, R, 
    point_calibrated_sensor_token_v, point_ego_pose_token_v, 
    cam_token_v, min_dist=1.0, return_img=True,
    sem_mask=sem_mask)

from tools.projection.pc2img import proj_lidar2img
from tools.projection.img_aug import merge_images_yaw, fit_box_cv, expand_box, crop_box_img, paste_box_img, merge_images_pitch_torch, draw_box_boundary


from mmengine import is_list_of
from mmcv.transforms import BaseTransform, Compose

# @TRANSFORMS.register_module(force=True)
class _PolarMix(BaseTransform):
    """PolarMix data augmentation.
    NOTE: This transform must be applied before GlobalRotScaleTrans.

    The polarmix transform steps are as follows:

        1. Another random point cloud is picked by dataset.
        2. Exchange sectors of two point clouds that are cut with certain
           azimuth angles.
        3. Cut point instances from picked point cloud, rotate them by multiple
           azimuth angles, and paste the cut and rotated instances.

    Required Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)
    - dataset (:obj:`BaseDataset`)

    Modified Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)

    Args:
        instance_classes (List[int]): Semantic masks which represent the
            instance.
        swap_ratio (float): Swap ratio of two point cloud. Defaults to 0.5.
        rotate_paste_ratio (float): Rotate paste ratio. Defaults to 1.0.
        pre_transform (Sequence[dict], optional): Sequence of transform object
            or config dict to be composed. Defaults to None.
        prob (float): The transformation probability. Defaults to 1.0.
    """

    def __init__(self,
                 instance_classes: List[int],
                 swap_ratio: float = 0.5,
                 rotate_paste_ratio: float = 1.0,
                 pre_transform: Optional[Sequence[dict]] = None,
                 prob: float = 1.0) -> None:
        assert is_list_of(instance_classes, int), \
            'instance_classes should be a list of int'
        self.instance_classes = instance_classes
        self.swap_ratio = swap_ratio
        self.rotate_paste_ratio = rotate_paste_ratio

        self.prob = prob
        if pre_transform is None:
            self.pre_transform = None
        else:
            self.pre_transform = Compose(pre_transform)

    def polar_mix_transform(self, input_dict: dict, mix_results: dict, angle=None) -> dict:
        """PolarMix transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            mix_results (dict): Mixed dict picked from dataset.

        Returns:
            dict: output dict after transformation.
        """
        points = input_dict['points']
        mix_points = mix_results['points']
        mix_pts_semantic_mask = mix_results['pts_semantic_mask']
        pts_semantic_mask = input_dict['pts_semantic_mask']

        block_cp = []
        block_cp_instlabel, block_cp_semlabel = [], []
        block_ro, block_ro_instlabel, block_ro_semlabel = [], [], []
        out_im_list = []
        binary_mask = torch.ones(len(points))
        binary_mask_orig = torch.ones(len(points))
        
        mix_panoptic = False
        if 'pts_instance_mask' in mix_results:
            mix_instance_mask = mix_results['pts_instance_mask']
            mix_instance_mask += (1000<<16) # not overlap id
            pts_instance_mask = input_dict['pts_instance_mask']
            mix_panoptic = True

        # 1. swap point cloud
        if np.random.random() < self.swap_ratio:
            # if angle is not None: 
            #     start_angle = angle
            # else:
            # print(f'********start angle {start_angle}, {start_angle/np.pi*180}')

            start_angle = (np.random.random() - 1) * np.pi  # -pi~0
            end_angle = start_angle + np.pi
            # calculate horizontal angle for each point
            yaw = -torch.atan2(points.coord[:, 1], points.coord[:, 0])
            mix_yaw = -torch.atan2(mix_points.coord[:, 1], mix_points.coord[:,
                                                                            0])

            # select points in sector
            idx = (yaw <= start_angle) | (yaw >= end_angle)
            mix_idx = (mix_yaw > start_angle) & (mix_yaw < end_angle)

            # swap
            a, b = points[idx].shape[0], mix_points[mix_idx].shape[0]
            block_orig = points[idx].coord[:, :3]
            block_swap = mix_points[mix_idx].coord[:, :3]
            block_sem_orig = pts_semantic_mask[idx.numpy()]
            block_sem_swap = mix_pts_semantic_mask[mix_idx.numpy()]
            
            swap_points = points.cat([points[idx], mix_points[mix_idx]])
            swap_pts_semantic_mask = np.concatenate(
                (pts_semantic_mask[idx.numpy()],
                 mix_pts_semantic_mask[mix_idx.numpy()]),
                axis=0)
            
            if mix_panoptic:
                pts_instance_mask = np.concatenate(
                    (pts_instance_mask[idx.numpy()],
                    mix_instance_mask[mix_idx.numpy()]),
                    axis=0)        
            
            # print(f'2********point (mixed) shape {swap_points.shape}, binary mask shape {binary_mask.shape}, a {a}, b {b}')
            
            # update points and binary mask
            points = swap_points
            pts_semantic_mask = swap_pts_semantic_mask
            binary_mask = torch.ones(len(points))
            binary_mask[a:a+b] = 0
            binary_mask_orig[a:] = 0

            ########## image augmentation for yaw swap ##########
            image_list_orig = input_dict['images']
            image_list_mix = mix_results['images']
            image_list_orig = [torch.tensor(img) for img in image_list_orig]
            image_list_mix  = [torch.tensor(img) for img in image_list_mix]
            
            lidar2img_orig, lidar2img_mix = input_dict['lidar2img'], mix_results['lidar2img']
            lidar2img_orig = list(lidar2img_orig.values())
            lidar2img_mix  = list(lidar2img_mix.values())
            img_size = input_dict['img_shape'][:2] # (360, 630)
            ori_size = input_dict['ori_shape'] # (900, 1600)
            img_scale = input_dict['scale_factor'][0] # 0.4
            assert img_size[0] == ori_size[0] * img_scale
            
            for v in range(len(image_list_orig)):
                im_orig, im_mix = image_list_orig[v].clone(), image_list_mix[v].clone()
                block_points_img, mask = proj_lidar2img(block_orig, lidar2img_orig[v], 
                                                        img_size=(ori_size[1], ori_size[0]), 
                                                        min_dist=1.0)
                block_points_img = block_points_img * img_scale
                l, r = draw_polar_boundary(block_points_img.numpy())

                img_new = merge_images_yaw(im_orig, im_mix, l, r)
                # plt.imshow(img_new[:,:,[2,1,0]])
                # plt.show()
                out_im_list.append(img_new)
            input_dict['images'] = out_im_list
            ########## image augmentation ##########
            
        # 2. rotate-pasting
        if np.random.random() < self.rotate_paste_ratio:
            # extract instance points
            instance_points, instance_pts_semantic_mask = [], []
            if mix_panoptic:
                instance_pts_instance_mask = []
            for instance_class in self.instance_classes:
                mix_idx = mix_pts_semantic_mask == instance_class
                instance_points.append(mix_points[mix_idx])
                instance_pts_semantic_mask.append(
                    mix_pts_semantic_mask[mix_idx])
                if mix_panoptic:
                    instance_pts_instance_mask.append(mix_instance_mask[mix_idx])
            
            block_cp = instance_points.copy()
            block_cp_instlabel = instance_pts_instance_mask.copy()
            block_cp_semlabel  = instance_pts_semantic_mask.copy()
            # print(f'4******** add instance points, number of inst {np.unique(instance_pts_semantic_mask).shape}')
            
            instance_points = mix_points.cat(instance_points)
            instance_pts_semantic_mask = np.concatenate(
                instance_pts_semantic_mask, axis=0)
            if mix_panoptic:
               instance_pts_instance_mask = np.concatenate(
                instance_pts_instance_mask, axis=0) 
            # bc = instance_points.shape[0]

            # # rotate-copy
            copy_points = [instance_points]
            copy_pts_semantic_mask = [instance_pts_semantic_mask]
            if mix_panoptic:
                copy_pts_instance_mask = [instance_pts_instance_mask]
            angle_list = [
                # angle * np.pi,
                # 0.5 * np.pi * 2 / 3,
                # np.random.random() * np.pi * 2 / 3,
                # (np.random.random() + 1) * np.pi * 2 / 3
            ]
            print(f'$$$$$$$$$$$$$$$$ Warning: instance rotate has not implemented yet!!!! ')
            for angle in angle_list:
                print(f'instance rotate angle at: {angle}, {angle/np.pi*180}')
                new_points = instance_points.clone()
                # new_points.rotate(angle, axis=0)
                copy_points.append(new_points)
                copy_pts_semantic_mask.append(instance_pts_semantic_mask)
                if mix_panoptic:
                    copy_pts_instance_mask.append(instance_pts_instance_mask)
                block_ro.append(new_points)
                block_ro_instlabel.append(instance_pts_instance_mask.copy())
                block_ro_semlabel.append(instance_pts_semantic_mask.copy())
                
            copy_points = instance_points.cat(copy_points)
            copy_pts_semantic_mask = np.concatenate(
                copy_pts_semantic_mask, axis=0)
            if mix_panoptic:
                copy_pts_instance_mask = np.concatenate(
                copy_pts_instance_mask, axis=0)

            c = copy_points.shape[0]
            points = points.cat([points, copy_points])
            pts_semantic_mask = np.concatenate(
                (pts_semantic_mask, copy_pts_semantic_mask), axis=0)
            if mix_panoptic:
                pts_instance_mask = np.concatenate(
                (pts_instance_mask, copy_pts_instance_mask), axis=0)
            
            # save binary mask
            binary_mask = torch.cat([binary_mask, torch.zeros(c)])  # Mark rotated-pasted points as modified
            binary_mask_orig = binary_mask_orig # pasting does not change the original points
            # print(f'3********point shape {points.shape}, added point shape {c}, binary mask shape {binary_mask.shape}')

            ########## image augmentation ##########
            if len(out_im_list) == 0: # if not swapped
                out_im_list = input_dict['images']
            
            # TODO-YINING: pack pre-processings into a function
            image_list_orig = out_im_list
            image_list_mix = mix_results['images']
            image_list_orig = [torch.tensor(img) for img in image_list_orig]
            image_list_mix  = [torch.tensor(img) for img in image_list_mix]
            img_size = input_dict['img_shape'][:2] # (360, 630)
            ori_size = input_dict['ori_shape'] # (900, 1600)
            img_scale = input_dict['scale_factor'][0] # 0.4
            assert img_size[0] == ori_size[0] * img_scale
            
            lidar2img_orig, lidar2img_mix = input_dict['lidar2img'], mix_results['lidar2img']
            lidar2img_orig = list(lidar2img_orig.values())
            lidar2img_mix  = list(lidar2img_mix.values())
            
            cp_coord = torch.cat([inst.coord for inst in block_cp])
            cp_instlabel = np.concatenate(block_cp_instlabel) # at least know the number of cp
            cp_semlabel  = np.concatenate(block_cp_semlabel)
            N = binary_mask.shape[0]
            N_cp = cp_instlabel.shape[0]
            N_ro = 0
            
            cp_mask = np.zeros(binary_mask.shape).astype(np.bool_)
            cp_mask[N-(N_cp+N_ro):N-N_ro] = 1
            
            for i in range(len(image_list_orig)):
                im_orig = image_list_orig[i].numpy()
                im_1_copy = im_orig.copy()
                im_mix = image_list_mix[i].numpy()
                cp_coord_img, mask = proj_lidar2img(cp_coord, lidar2img_orig[i], 
                                                    img_size=(ori_size[1], ori_size[0]), 
                                                    min_dist=1.0)
                cp_coord_img = cp_coord_img * img_scale 

                cp_coord_img = cp_coord_img.numpy()
                cp_instlabel_v = cp_instlabel[mask]
                cp_semlabel_v  = cp_semlabel[mask]
                coloring = np.array([color_map[k] for k in cp_semlabel_v])/255

                for inst_id in np.unique(cp_instlabel_v):
                    inst_coord = cp_coord_img[cp_instlabel_v == inst_id]
                    inst_cls = cp_semlabel_v[cp_instlabel_v == inst_id][0]
                    # inst_coord = inst_coord[:2, :].T
                    box = fit_box_cv(inst_coord)
                    ##1.  expand the box
                    # box = expand_box_proportional(box, 0.2, im_2_np.shape[:2])
                    box_ex = expand_box(box, 20, im_mix.shape[:2])
                    ##2.  crop
                    crop_img = crop_box_img(box_ex, im_mix)
                    # crop_img_list.append(crop_img)
                    ##3.  draw the box
                    im_1_copy = draw_dashed_box(im_1_copy, box_ex, thickness=5, dash_length=20)
                    # paste to orig image
                    im_1_copy = paste_box_img(box_ex, im_1_copy, crop_img)

                # plot_point_in_camview_2(cp_coord_img, coloring, im_1_copy, dot_size=3)
                out_im_list[i] = im_1_copy
            ########## image augmentation ##########
        
        input_dict['points'] = points
        input_dict['pts_semantic_mask'] = pts_semantic_mask
        if mix_panoptic:
            input_dict['pts_instance_mask'] = pts_instance_mask
            
        # input_dict['binary_mask'] = binary_mask
        if 'augment_mask' in input_dict:
            input_dict['augment_mask'] = input_dict['augment_mask'] & binary_mask  # intersaction the binary mask
            input_dict['augment_mask_orig'] = input_dict['augment_mask_orig'] & binary_mask_orig  
        else:
            input_dict['augment_mask'] = binary_mask # 1 for original, 0 for modified
            input_dict['augment_mask_orig'] = binary_mask_orig
        
        # input_dict['block_orig'] = block_orig
        # input_dict['block_swap'] = block_swap
        # input_dict['block_cp'] = block_cp
        # input_dict['block_cp_instlabel'] = block_cp_instlabel
        # input_dict['block_cp_semlabel'] = block_cp_semlabel
        input_dict['block_ro'] = block_ro
        input_dict['block_ro_instlabel'] = block_ro_instlabel
        input_dict['block_ro_semlabel'] = block_ro_semlabel
        input_dict['images'] = out_im_list
        return input_dict

    def transform(self, input_dict: dict) -> dict:
        """PolarMix transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: output dict after transformation.
        """
        if np.random.rand() > self.prob:
            return input_dict

        # assert 'dataset' in input_dict, \
        #     '`dataset` is needed to pass through PolarMix, while not found.'
        # dataset = input_dict['dataset']

        # # get index of other point cloud
        # index = np.random.randint(0, len(dataset))

        # mix_results = dataset.get_data_info(index)

        # if self.pre_transform is not None:
        #     # pre_transform may also require dataset
        #     mix_results.update({'dataset': dataset})
        #     # before polarmix need to go through
        #     # the necessary pre_transform
        #     mix_results = self.pre_transform(mix_results)
        #     mix_results.pop('dataset')
        mix_results = input_dict['mix_results'] # load another point cloud
        if 'angle' in input_dict:
            angle = input_dict['angle']
        else:
            angle = None
        input_dict = self.polar_mix_transform(input_dict, mix_results, angle=angle)

        return input_dict

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(instance_classes={self.instance_classes}, '
        repr_str += f'swap_ratio={self.swap_ratio}, '
        repr_str += f'rotate_paste_ratio={self.rotate_paste_ratio}, '
        repr_str += f'pre_transform={self.pre_transform}, '
        repr_str += f'prob={self.prob})'
        return repr_str



from mmengine import is_list_of, is_tuple_of
from mmcv.transforms import BaseTransform, Compose, RandomResize, Resize

# @TRANSFORMS.register_module(force=True)
class _LaserMix(BaseTransform):
    """LaserMix data augmentation. 
    NOTE: This transform must be applied before GlobalRotScaleTrans.

    The lasermix transform steps are as follows:

        1. Another random point cloud is picked by dataset.
        2. Divide the point cloud into several regions according to pitch
           angles and combine the areas crossly.

    Required Keys: 

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)
    - dataset (:obj:`BaseDataset`)

    Modified Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)

    Args:
        num_areas (List[int]): A list of area numbers will be divided into.
        pitch_angles (Sequence[float]): Pitch angles used to divide areas.
        pre_transform (Sequence[dict], optional): Sequence of transform object
            or config dict to be composed. Defaults to None.
        prob (float): The transformation probability. Defaults to 1.0.
    """

    def __init__(self,
                 num_areas: List[int],
                 pitch_angles: Sequence[float],
                 pre_transform: Optional[Sequence[dict]] = None,
                 prob: float = 1.0) -> None:
        assert is_list_of(num_areas, int), \
            'num_areas should be a list of int.'
        self.num_areas = num_areas

        assert len(pitch_angles) == 2, \
            'The length of pitch_angles should be 2, ' \
            f'but got {len(pitch_angles)}.'
        assert pitch_angles[1] > pitch_angles[0], \
            'pitch_angles[1] should be larger than pitch_angles[0].'
        self.pitch_angles = pitch_angles

        self.prob = prob
        if pre_transform is None:
            self.pre_transform = None
        else:
            self.pre_transform = Compose(pre_transform)

    def laser_mix_transform(self, input_dict: dict, mix_results: dict) -> dict:
        """LaserMix transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            mix_results (dict): Mixed dict picked from dataset.

        Returns:
            dict: output dict after transformation.
        """
        mix_points = mix_results['points']
        mix_pts_semantic_mask = mix_results['pts_semantic_mask']

        points = input_dict['points']
        pts_semantic_mask = input_dict['pts_semantic_mask']
        binary_mask = None
        binary_mask_orig = torch.zeros(len(points), dtype=torch.uint8)


        rho = torch.sqrt(points.coord[:, 0]**2 + points.coord[:, 1]**2)
        pitch = torch.atan2(points.coord[:, 2], rho)
        pitch = torch.clamp(pitch, self.pitch_angles[0] + 1e-5,
                            self.pitch_angles[1] - 1e-5)

        mix_rho = torch.sqrt(mix_points.coord[:, 0]**2 +
                             mix_points.coord[:, 1]**2)
        mix_pitch = torch.atan2(mix_points.coord[:, 2], mix_rho)
        mix_pitch = torch.clamp(mix_pitch, self.pitch_angles[0] + 1e-5,
                                self.pitch_angles[1] - 1e-5)

        num_areas = np.random.choice(self.num_areas, size=1)[0]
        angle_list = np.linspace(self.pitch_angles[1], self.pitch_angles[0],
                                 num_areas + 1)
        out_points = []
        out_pts_semantic_mask = []
        block_orig, block_mix = [], []
        
        mix_panoptic = False
        if 'pts_instance_mask' in mix_results:
            mix_instance_mask = mix_results['pts_instance_mask']
            mix_instance_mask += (1000<<16) # not overlap id
            pts_instance_mask = input_dict['pts_instance_mask']
            out_pts_instance_mask = []
            mix_panoptic = True

        for i in range(num_areas):
            # convert angle to radian
            start_angle = angle_list[i + 1] / 180 * np.pi
            end_angle = angle_list[i] / 180 * np.pi
            if i % 2 == 0:  # pick from original point cloud
                idx = (pitch > start_angle) & (pitch <= end_angle)
                selected_points = points[idx]
                
                out_points.append(points[idx])
                block_orig.append(points[idx])
                out_pts_semantic_mask.append(pts_semantic_mask[idx.numpy()])
                if mix_panoptic:
                    out_pts_instance_mask.append(pts_instance_mask[idx.numpy()])
                if binary_mask == None:
                    binary_mask = torch.ones(selected_points.shape[0], dtype=torch.uint8)
                else:
                    binary_mask = torch.cat([binary_mask, torch.ones(selected_points.shape[0], dtype=torch.uint8)])  # Mark as original
                binary_mask_orig[idx] = 1  
                
            else:  # pickle from mixed point cloud
                idx = (mix_pitch > start_angle) & (mix_pitch <= end_angle)
                selected_points = mix_points[idx]
                
                out_points.append(mix_points[idx])
                block_mix.append(mix_points[idx])
                out_pts_semantic_mask.append(
                    mix_pts_semantic_mask[idx.numpy()])
                if mix_panoptic:
                    out_pts_instance_mask.append(mix_instance_mask[idx.numpy()])
                
                binary_mask = torch.cat([binary_mask, torch.zeros(selected_points.shape[0], dtype=torch.uint8)])  # Mark as modified

        # save pts blocks
        # input_dict['pts_blocks_orig'] = block_orig
        # input_dict['pts_blocks_mix'] = block_mix
        # input_dict['pts_semantic_mask_blocks'] = out_pts_semantic_mask
        
        ########## image augmentation ##########
        out_im_list = []
        image_list_orig = input_dict['images']
        image_list_mix  = mix_results['images']
        image_list_orig = [torch.tensor(img) for img in image_list_orig]
        image_list_mix  = [torch.tensor(img) for img in image_list_mix]
        img_size = input_dict['img_shape']
        ori_img_size = input_dict['ori_shape'] # (900, 1600)
        img_scale = input_dict['scale_factor'][0] # 0.4
        assert img_size[0] == ori_img_size[0] * img_scale
        # for view in input_dict["lidar2img"].keys():
        #     input_dict["lidar2img"][view] = input_dict["lidar2img"][view] @ trans_mat_inv
        #     input_dict["lidar2cam"][view] = input_dict["lidar2cam"][view] @ trans_mat_inv

        lidar2img_orig, lidar2img_mix = input_dict['lidar2img'], mix_results['lidar2img']
        lidar2img_orig = list(lidar2img_orig.values())
        lidar2img_mix  = list(lidar2img_mix.values())
        # aug_mat = input_dict['lidar_aug_matrix'] if 'lidar_aug_matrix' in input_dict else torch.eye(4)

        for v in range(len(image_list_orig)):
            im_orig, im_mix = image_list_orig[v].clone(), image_list_mix[v].clone()
            lidar2img_mix_v = lidar2img_mix[v] # @ torch.inverse(aug_mat)
            for i in range(len(block_mix)):
                pblock = block_mix[i].coord
                points_img_b, mask_b = proj_lidar2img(pblock, lidar2img_mix_v, 
                                                      img_size=(ori_img_size[1], ori_img_size[0]),  # w, h
                                                      min_dist=1.0)
                points_img_b = points_img_b*img_scale
                mixed_img, block = merge_images_pitch_torch(im_orig, im_mix, points_img_b)   
                im_orig = mixed_img
            # plt.imshow(im_orig[:,:,[2,1,0]])
            # plt.show()
            out_im_list.append(im_orig)
        ########## image augmentation ##########
            
                
            
        # print(f'1********point shape {points.shape}, binary mask shape {binary_mask.shape}')
        out_points = points.cat(out_points)
        # print(f'2********point shape {out_points.shape}')
    
        out_pts_semantic_mask = np.concatenate(out_pts_semantic_mask, axis=0)
        input_dict['points'] = out_points
        input_dict['pts_semantic_mask'] = out_pts_semantic_mask
        input_dict['binary_mask'] = binary_mask
        if 'augment_mask' in input_dict:
            input_dict['augment_mask'] = input_dict['augment_mask'] & binary_mask  # intersaction the binary mask
            input_dict['augment_mask_orig'] = input_dict['augment_mask_orig'] & binary_mask_orig
        else:
            input_dict['augment_mask'] = binary_mask # 1 for original, 0 for modified
            input_dict['augment_mask_orig'] = binary_mask_orig
        if mix_panoptic:
            out_pts_instance_mask = np.concatenate(out_pts_instance_mask, axis=0)
            input_dict['pts_instance_mask'] = out_pts_instance_mask
        input_dict['images'] = out_im_list
        return input_dict

    def transform(self, input_dict: dict) -> dict:
        """LaserMix transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: output dict after transformation.
        """
        if np.random.rand() > self.prob:
            return input_dict

        # assert 'dataset' in input_dict, \
        #     '`dataset` is needed to pass through LaserMix, while not found.'
        # dataset = input_dict['dataset']

        # get index of other point cloud
        # index = np.random.randint(0, len(dataset)) #TODO: not used for debug
        # mix_results = dataset.get_data_info(index)

        # if self.pre_transform is not None:
            # pre_transform may also require dataset
            # mix_results.update({'dataset': dataset})  #TODO: not used for debug
            # before lasermix need to go through
            # the necessary pre_transform
            # mix_results = self.pre_transform(mix_results)
            # mix_results.pop('dataset')

        
        mix_results = input_dict['mix_results'] # load another point cloud
        
        input_dict = self.laser_mix_transform(input_dict, mix_results)

        return input_dict

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(num_areas={self.num_areas}, '
        repr_str += f'pitch_angles={self.pitch_angles}, '
        repr_str += f'pre_transform={self.pre_transform}, '
        repr_str += f'prob={self.prob})'
        return repr_str



seg_offset = 1000
transforms=[
        [
        dict(
                type='_LaserMix',
                num_areas=[3, 4, 5, 6],
                pitch_angles=[-25, 3],
                pre_transform=[
                dict(
                        type='LoadPointsFromFile',
                        coord_type='LIDAR',
                        load_dim=5,
                        use_dim=3),
                dict(
                        type='_LoadAnnotations3D', # for nuScenes
                        with_bbox_3d=False,
                        with_label_3d=False,
                        with_panoptic_3d=True,
                        seg_3d_dtype='np.uint8',
                        seg_offset=seg_offset,
                        dataset_type='nuscenes'),
                dict(type='_PointSegClassMapping')
                ],
                prob=0.5)
        ],]

from mmdet3d.structures.points import BasePoints
from tools.projection.pc2img import get_lidar2img
import torch

input_dicts = []
for i in range(len(res_list)):
    point = res_list[i]['lidar_point']
    pts_semantic_mask = res_list[i]['pts_semantic_mask']
    pts_instance_mask = res_list[i]['pts_instance_mask']
    image_list = list(res_list[i]['imgs_meta']['img'].values())
    image_list = [torch.tensor(img) for img in image_list]
    lidar2cams = res_list[i]['imgs_meta']['lidar2cam']
    cam2imgs = res_list[i]['imgs_meta']['cam2img']
    lidar2imgs = {}
    for i in range(len(lidar2cams)):
        view = VIEWS[i]
        lidar2img = get_lidar2img(torch.tensor(lidar2cams[view]).float(),  torch.tensor(cam2imgs[view]).float())
        lidar2imgs[view] = lidar2img

    input_dict_i = {
    'points': BasePoints(point),
    'images': image_list,
    'lidar2cam': lidar2cams,
    'cam2img': cam2imgs,
    'lidar2img': lidar2imgs,
    'bbox3d_fields': [],
    'pts_semantic_mask': pts_semantic_mask,
    'pts_instance_mask': pts_instance_mask,
    # 'transformation_3d_flow': [],
    }
    input_dicts.append(input_dict_i)

def get_view_token(data_list, idx, view='CAM_FRONT'):
    sample_token_v = data_list[idx]['token']
    sample_v = nusc.get('sample', sample_token_v)
    pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
    point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
    cam_token_v = sample_v['data'][view]    
    return point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v 


import torch

# 1. rotate, scale, translate
# aug_ratscaletrans = GlobalRotScaleTrans_MM()
# 2. flip
# aug_flip = RandomFlip3D_MM()
# 3. laser mix
# ### make sure the input_dict has 'mix_results' key
s1, s2 = 2, 120
input_dict_1 = input_dicts[s1].copy()
sample_token_v1 = data['data_list'][s1]['token']
calib_t1, ego_t1, cam_t1 = get_view_token(data['data_list'], s1, view='CAM_FRONT')
input_dict_2 = input_dicts[s2].copy()
calib_t2, ego_t2, cam_t2 = get_view_token(data['data_list'], s2, view='CAM_FRONT')
input_dict_1['mix_results'] = input_dict_2

aug_lasermix = _LaserMix(num_areas=[4], pitch_angles=[-30, 10], pre_transform=None, prob=1.0)
out_dict = aug_lasermix(input_dict_1)
# 4. polarmix
# input_dict_1 = input_dicts[0]
# input_dict_2 = input_dicts[188]
# input_dict_1['mix_results'] = input_dict_2
# aug_polarmix = _PolarMix(instance_classes=[1,2,3,4,5,6,7,8,9], swap_ratio=1.0, rotate_paste_ratio=1.0, pre_transform=None, prob=1.0) # [9,14,15,16,17,18,21,2,3,4,6,12,22]
# out_dict = aug_polarmix(input_dict_1)

print(out_dict['points'].shape, out_dict['binary_mask'].shape, out_dict['pts_semantic_mask'].shape)


color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }   
label_map = {
    1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
    9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
    22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
    }
        




plot_point_in_camview_2(points_img, coloring, im_p, dot_size=5)





# from tools.projection.pc2img import map_pointcloud_to_image_2

### load point cloud from pkl file
point_coor = out_dict['points'].coord.numpy()
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)

# ### rotation
R = np.eye(4)
# R = out_dict['lidar_aug_matrix']
# point_coor = point_coor @ R

### load semantic mask
sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
binary_mask = out_dict['binary_mask'].numpy()

### map point cloud to image
# #1. plot binary mask, where 1 is modified points
# point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
#     nusc, point_coor, 
#     point_calibrated_sensor_token_v, point_ego_pose_token_v, 
#     cam_token_v, min_dist=1.0, return_img=True,
#     sem_mask=binary_mask,
#     use_label_map=False)
#2. plot semantic mask(contains modified points and original points)
# point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
#     nusc, point_coor, 
#     point_calibrated_sensor_token_v, point_ego_pose_token_v, 
#     cam_token_v, min_dist=1.0, return_img=True,
#     sem_mask=sem_mask,
#     use_label_map=False)
# # 3. plot semantic mask(only original points)
bin_mask = binary_mask == 1
point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
    nusc, point_coor, 
    calib_t1, ego_t1, cam_t1,
    min_dist=1.0, return_img=True,
    sem_mask=sem_mask,
    use_label_map=False)



plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p], im_p, dot_size=5)

#1. plot binary mask, where 1 is modified points
point_cam_coords1, coloring_p, im_p, mask_p = map_pointcloud_to_image_2(
    nusc, point_coor, 
    point_calibrated_sensor_token_v, point_ego_pose_token_v, 
    cam_token_v, min_dist=1.0, return_img=True,
    sem_mask=binary_mask,
    use_label_map=False)


plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p], im_p, dot_size=5)

def proj_lidar2img(points, lidar2img, img_size=(1600, 900), min_dist=1.0):
    """Project lidar points to image plane.
    Args:
        points (torch.Tensor): Lidar points. (N, 3)
        lidar2img (torch.Tensor): Lidar to image matrix. (4, 4)
        img_size (tuple): Image size.
        min_dist (float): Minimum distance to the camera.
    """
    points = torch.cat([points, torch.ones(points.shape[0],1)], dim=1)
    points_img = (lidar2img @ points.T)
    depths = points_img[2, :]
    points_img = points_img / points_img[2]
    img_W, img_H = img_size
    
    mask = torch.ones(depths.shape[0], dtype=bool)
    mask = torch.logical_and(mask, depths > min_dist)
    mask = torch.logical_and(mask, points_img[0, :] > 1)
    mask = torch.logical_and(mask, points_img[0, :] < img_W - 1)
    mask = torch.logical_and(mask, points_img[1, :] > 1)
    mask = torch.logical_and(mask, points_img[1, :] < img_H - 1)
    
    points_img = points_img[:, mask]
    return points_img[:2, :].T, mask

# points = input_dicts[s1]['points'].coord.clone()
# lidar2img = input_dicts[s1]['lidar2img']
# sem_mask = input_dicts[s1]['pts_semantic_mask']

# points_img, mask = proj_lidar2img(points, lidar2img['CAM_FRONT'], img_size=(1600, 900), min_dist=1.0)
# sem_mask = sem_mask[mask]
# coloring = np.array([color_map[i] for i in sem_mask])/255
# sem_mask.shape, points_img.shape, coloring.shape

def plot_point_in_camview_2(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[:, 0], points[:, 1], c=coloring, s=dot_size)
    ax.axis('off')
    return fig, ax

def gen_block_mask_curve(points, image_shape=(900, 1600), window_size=10):
    upper_points, lower_points = sliding_window_max_min(points, window_size)
    
    if upper_points.shape[0] == 0 or lower_points.shape[0] == 0:
        return np.zeros(image_shape)
    # 4. 分别拟合上界和下界
    coeffs_upper = np.polyfit(upper_points[:, 0], upper_points[:, 1], deg=2)
    coeffs_lower = np.polyfit(lower_points[:, 0], lower_points[:, 1], deg=2)

    # 生成拟合曲线
    x_fit = np.linspace(0, 1600, 1600)
    y_fit_upper = np.polyval(coeffs_upper, x_fit)
    y_fit_lower = np.polyval(coeffs_lower, x_fit)

    # 5. 生成mask
    mask = np.zeros(image_shape)
    x_img = np.linspace(0, image_shape[1] - 1, len(x_fit))

    for i, x in enumerate(x_img):
        # 确保 y 值在图像范围内
        y_upper = int(np.clip(y_fit_upper[i], 0, image_shape[0] - 1))
        y_lower = int(np.clip(y_fit_lower[i], 0, image_shape[0] - 1))

        # 2. 将位于 [y_lower, y_upper] 范围内的像素值设为 1
        mask[y_lower:y_upper, int(x)] = 1
    # for i in range(1600):
    #     mask[i, int(y_fit_lower[i]):int(y_fit_upper[i])] = 1
    return mask

# 2. 滑动窗口计算上下界（间隔采样）
def sliding_window_max_min(points, window_size):
    upper_points = []
    lower_points = []
    
    x_vals = points[:, 0]
    y_vals = points[:, 1]
    
    # 计算滑动窗口内的最大值和最小值
    for i in range(0, len(x_vals), window_size):
        x_window = x_vals[i:i+window_size]
        y_window = y_vals[i:i+window_size]
        
        if len(x_window) > 0:
            max_y = np.max(y_window)
            min_y = np.min(y_window)
            upper_points.append([np.mean(x_window), max_y])
            lower_points.append([np.mean(x_window), min_y])
    
    return np.array(upper_points), np.array(lower_points)

def draw_box_boundary(points, mode):
    box = fit_box_cv(points)
    if   mode == 'vertical':   k = 0
    elif mode == 'horizontal': k = 1
    else: raise ValueError('mode should be vertical or horizontal')
    
    sorted_by_x = sorted(box, key=lambda p: p[k]) # if 'vertical': left&right sides, if 'horizontal': top&bottom sides
    side1 = sorted_by_x[:2]  
    side1_mid = np.mean(side1, axis=0)  

    side2 = sorted_by_x[-2:] 
    side2_mid = np.mean(side2, axis=0) 
    return side1_mid[k].astype(int), side2_mid[k].astype(int)

def merge_images_pitch(im1, im2, point_block):
    """merge two images with pitch angle according to point block"""
    assert point_block.shape[1] == 2, 'point block should be (n, 2)'
    
    t, b = draw_box_boundary(point_block, 'horizontal')

    orig = np.array(im1).copy()
    new = np.array(im2).copy()
    mask = np.zeros_like(orig)
    
    orig[t:b,:,:] = new[t:b,:,:]
    mask[t:b,:,:] = 1
    return orig, mask

def merge_images_pitch_torch(im1, im2, point_block):
    """merge two images with pitch angle according to point block"""
    assert point_block.shape[1] == 2, 'point block should be (n, 2)'
    if isinstance(im1, np.ndarray):
        im1 = torch.from_numpy(im1).to(point_block.device)
    if isinstance(im2, np.ndarray):
        im2 = torch.from_numpy(im2).to(point_block.device)        
    t, b = draw_box_boundary(point_block.cpu().numpy(), 'horizontal')
    print(f'top: {t}, bottom: {b}')
    orig = im1.clone()
    new = im2.clone()
    # print(f'orig shape: {orig.shape}, new shape: {new.shape}')
    mask = torch.zeros_like(orig)
    orig[t:b,:,:] = new[t:b,:,:]
    mask[t:b,:,:] = 1
    # plt.imshow(orig[:,:,[2,1,0]])
    # plt.show()
    return orig, mask

def proj_lidar2img(points, lidar2img, img_size=(1600, 900), min_dist=1.0):
    """Project lidar points to image plane.
    Args:
        points (torch.Tensor): Lidar points. (N, 3)
        lidar2img (torch.Tensor): Lidar to image matrix. (4, 4)
        img_size (tuple): Image size.
        min_dist (float): Minimum distance to the camera.
    """
    points = torch.cat([points, torch.ones(points.shape[0],1)], dim=1)
    points_img = (lidar2img @ points.T)
    depths = points_img[2, :]
    points_img = points_img / points_img[2]
    img_W, img_H = img_size
    
    mask = torch.ones(depths.shape[0], dtype=bool)
    mask = torch.logical_and(mask, depths > min_dist)
    mask = torch.logical_and(mask, points_img[0, :] > 1)
    mask = torch.logical_and(mask, points_img[0, :] < img_W - 1)
    mask = torch.logical_and(mask, points_img[1, :] > 1)
    mask = torch.logical_and(mask, points_img[1, :] < img_H - 1)

    points_img = points_img[:, mask]
    return points_img[:2, :].T, mask



import matplotlib.pyplot as plt
import cv2

s1, s2 = 55,66
v = 0
view = VIEWS[0]

input_dict_1 = input_dicts[s1].copy()
sample_token_v1 = data['data_list'][s1]['token']
calib_t1, ego_t1, cam_t1 = get_view_token(data['data_list'], s1, view=view)
input_dict_2 = input_dicts[s2].copy()
calib_t2, ego_t2, cam_t2 = get_view_token(data['data_list'], s2, view=view)
input_dict_1['mix_results'] = input_dict_2

input_dict_1['images'] = [torch.tensor(img) for img in input_dict_1['images']]
input_dict_1['mix_results']['images'] = [torch.tensor(img) for img in input_dict_1['mix_results']['images']]

translation_std = [0.1, 0.1, 0.1]
aug_lasermix = _LaserMix(num_areas=[3], pitch_angles=[-30, 10], pre_transform=None, prob=1.0)
out_dict = aug_lasermix(input_dict_1)

################# Global Rotate Scale and Transform ##############
# aug_ratscaletrans = GlobalRotScaleTrans_MM(translation_std=translation_std)
# out_dict['pcd_scale_factor'] = 0.1
# out_dict = aug_ratscaletrans(out_dict)
# lidar_aug_matrix = out_dict['lidar_aug_matrix']
################# Global Rotate Scale and Transform ##############

# out_im_list = out_dict['images']
# for v in range(len(out_im_list)):
#     plt.imshow(out_im_list[v][:,:,[2,1,0]])
#     plt.show()

# # pts info
# point_coor = out_dict['points'].coord.numpy()
# point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)
point_coor = out_dict['points'].coord
# # aug transform
# ## 1. @
# aug = lidar_aug_matrix[:3, :3]
# aug = np.linalg.inv(aug)
# point_coor = (point_coor @ aug).to(point_coor)
# ## 2. _LidarPointCloud free
# # point_coor = torch.cat([point_coor, torch.ones(point_coor.shape[0],1)], dim=1)
# # point_coor_ = _LidarPointCloud.pack_points(point_coor.numpy())
# # point_coor_.rotate(aug)
# # point_coor = point_coor_.points
# # point_coor = torch.tensor(point_coor).T[:, :3]

sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
# # binary_mask = out_dict['binary_mask'].numpy()
# # bin_mask = binary_mask == 1

# # # block info
# # pts_blocks_orig = out_dict['pts_blocks_orig']
# # pts_blocks_mix = out_dict['pts_blocks_mix']
# # pts_semantic_mask_blocks = out_dict['pts_semantic_mask_blocks']


lidar2img_1 = input_dicts[s1]['lidar2img']
lidar2img_2 = input_dicts[s2]['lidar2img']



# im_2 = get_Image_from_token(nusc, cam_t2)
# im_1 = get_Image_from_token(nusc, cam_t1)
im_mixed  = out_dict['images'][0].numpy()
# resize img
scale = 1  # 缩小一半
new_width = int(im_mixed.shape[1] * scale)
new_height = int(im_mixed.shape[0] * scale)
resized_img = cv2.resize(im_mixed, (new_width, new_height), interpolation=cv2.INTER_LINEAR)
trans_mat = scale*torch.eye(3)
trans_mat_pad = torch.eye(4)
trans_mat_pad[:3,:3] = trans_mat

# trans_mat = torch.linalg.inv(trans_mat)

points_img, mask = proj_lidar2img(point_coor, trans_mat_pad@lidar2img_1[view], img_size=(new_width, new_height), min_dist=1.0)
sem_mask = sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

plot_point_in_camview_2(points_img, coloring, resized_img, dot_size=5)

# # #################### augment images ####################
# # block = np.zeros((900, 1600))
# # # window_size = 20  # 定义滑动窗口的大小，决定了间隔采样的频率
# # im_2_np = np.array(im_2)
# # im_1_np = np.array(im_1)

# # # show the mixed point cloud 
# # for i in range(len(pts_blocks_mix)):
# #     pblock = pts_blocks_mix[i].coord
# #     points_img_b, mask_b = proj_lidar2img(pblock, lidar2img_2[view], img_size=(1600, 900), min_dist=1.0)
# #     print(f'block {i} shape: {points_img_b.shape}')
# #     # sem_mask_b = pts_semantic_mask_blocks[i][mask_b] #TODO: shape not match
# #     # coloring_b = np.array([color_map[i] for i in sem_mask_b[i]])/255 
    
# #     # # 1. fit the curve boundary
# #     # pp = point_cam_coords_block[:, mask_p_b][:2, :].T
# #     # block_mask = gen_block_mask(pp)
# #     # 2. fit the box boundary
# #     mixed_img, block = merge_images_pitch_torch(im_1_np, im_2_np, points_img_b)   
# #     im_1_np = mixed_img
# #     # # visualize the block mask
# #     # # plot_point_in_camview_2(points_img_b, coloring_b, im_2_np, dot_size=5)
    
# #     # # cut the block of mixed image to the original image
# #     # if len(block.shape) == 2:
# #     #     block = np.expand_dims(block, -1)
# #     # new = block * im_2_np / 255
# #     # plt.imshow(new)
# #     # plt.axis('off')
# #     # plt.show()
    
# # # res = block*im_2_np + (1-block)*im_1_np
# # # plt.figure(figsize=(9, 16))
# # # plt.imshow(mixed_img)
# # # plt.axis('off')
# # plot_point_in_camview_2(points_img, coloring, mixed_img, dot_size=5)





import numpy as np

def calculate_rotation_angle_x(matrix):
    """
    计算给定旋转矩阵绕 X 轴的旋转角度（单位为度）。
    
    参数:
    - matrix: 4x4 旋转矩阵 (numpy array)

    返回:
    - angle_x: 绕 X 轴的旋转角度（单位为度）
    """
    # 提取 3x3 旋转子矩阵
    rotation_matrix = matrix[:3, :3]
    
    # 计算绕 X 轴的旋转角度
    # 由于旋转矩阵的形式，绕 X 轴的旋转主要体现在 rotation_matrix[1, 1] 和 rotation_matrix[1, 2] 元素
    angle_x_rad = np.arctan2(rotation_matrix[1, 2], rotation_matrix[1, 1])
    
    # 将弧度转换为角度
    angle_x_deg = np.degrees(angle_x_rad)
    
    return angle_x_deg

# 示例使用
rotate_matrix = np.array([[ 1.        ,  0.        ,  0.        ,  0.        ],
                          [ 0.        ,  0.96887529,  0.24754946,  0.        ],
                          [ 0.        , -0.24754946,  0.96887529,  0.        ],
                          [ 0.        ,  0.        ,  0.        ,  1.        ]])

angle_x = calculate_rotation_angle_x(rotate_matrix)
print(f"绕 X 轴的旋转角度为: {angle_x:.2f} 度")


# R_pad = np.array([[ 0., -0.,  1.,  0.],
#        [ 0.,  1.,  0.,  0.],
#        [-1.,  0.,  0.,  0.],
#        [ 0.,  0.,  0.,  1.]]) 
R_scale = np.eye(4)
R_scale[:3, :3] = np.eye(3) * 0.2
R_scale_inv = np.linalg.inv(R_scale)

point_coor_rot = point_coor @ R_scale[:3, :3] @ R_scale_inv[:3, :3]
point_coor_rot = point_coor_rot.float()
points_img, mask = proj_lidar2img(point_coor_rot, lidar2img_1[view], img_size=(1600, 900), min_dist=1.0)
sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
sem_mask = sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255
plot_point_in_camview_2(points_img, coloring, im_1, dot_size=5)


points_img.shape







# t, b = draw_box_boundary(pp, 'horizontal')
# print(f'top: {t}, bottom: {b}')
# # im_2_np = cv2.line(im_2_np, tuple(side[0].astype(np.int32)), tuple(side[1].astype(np.int32)), (77, 255, 0), 2)
# # plt.imshow(im_2_np)
# im1 = im_1_np.copy()
# im2 = im_2_np.copy()

# new = np.array(im2).copy()
# orig = np.array(im1).copy()

# # new[:, l:r, :] = orig[:, l:r, :]
# orig[t:b,:,:] = new[t:b,:,:]
# plt.imshow(orig)
# plt.imshow(block)
plt.imshow(mixed_img)

# box = fit_box_cv(points)
# if   mode == 'vertical':   k = 0
# elif mode == 'horizontal': k = 1
k = 1
# else: raise ValueError('mode should be vertical or horizontal')

sorted_by_x = sorted(box, key=lambda p: p[k]) # if 'vertical': left&right sides, if 'horizontal': top&bottom sides
side1 = sorted_by_x[:2]  
side1_mid = np.mean(side1, axis=0)  

side2 = sorted_by_x[-2:] 
side2_mid = np.mean(side2, axis=0) 
side1_mid, side2_mid





point_cam_coords_block_2, coloring_p_2, im_p_2, mask_p_2 = map_pointcloud_to_image_2(
        nusc, pblock_coor, 
        calib_t2, ego_t2, cam_t2,
        min_dist=1.0, return_img=True,
        sem_mask=b,
        use_label_map=False)

plot_point_in_camview(point_cam_coords_block_2[:, mask_p_2], coloring_p_2[mask_p_2], im_p_2, dot_size=5)

# block_mask = gen_block_mask(pp.T)
if len(block.shape) == 2:
    block = np.expand_dims(block, -1)
new = block * im_p_2 / 255
plt.imshow(new)
plt.axis('off')
plt.show()


orig = ((1 - block) * im_p_b) / 255
plt.imshow(orig, cmap='gray')
plt.axis('off')
plt.show()

# mixed
plt.imshow(orig+new)
plt.axis('off')
plt.show()

a = plot_point_in_camview(point_cam_coords1[:, mask_p], coloring_p[mask_p], orig+new, dot_size=5)

# colorize with semantic
sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
binary_mask = out_dict['binary_mask'].numpy()

# block_mask = binary_mask == 0

import numpy as np
import matplotlib.pyplot as plt


points = pp.T # np.vstack([x_vals, y_vals_noisy]).T


# 3. 使用滑动窗口计算上层点和下层点
upper_points, lower_points = sliding_window_max_min(points, window_size)

# 4. 分别拟合上界和下界
coeffs_upper = np.polyfit(upper_points[:, 0], upper_points[:, 1], deg=2)
coeffs_lower = np.polyfit(lower_points[:, 0], lower_points[:, 1], deg=2)

# 生成拟合曲线
x_fit = np.linspace(0, 1600, 1600)
y_fit_upper = np.polyval(coeffs_upper, x_fit)
y_fit_lower = np.polyval(coeffs_lower, x_fit)

# 5. 可视化结果
plt.figure(figsize=(8, 6))
plt.scatter(points[:, 0], points[:, 1], s=10, label='Points', alpha=0.5)
plt.plot(x_fit, y_fit_upper, color='red', label='Upper Bound Fit', linewidth=2)
plt.plot(x_fit, y_fit_lower, color='blue', label='Lower Bound Fit', linewidth=2)
plt.xlabel('X')
plt.ylabel('Y')
plt.title('Upper and Lower Bound Fitting with Sliding Window')
plt.legend()
plt.show()


upper_points, lower_points

import numpy as np
import matplotlib.pyplot as plt

# 假设图像大小为 512x512
image_shape = (900, 1600)

# 生成一个空的掩码
mask = np.zeros(image_shape, dtype=np.uint8)

# # x_fit 和 y_fit_upper, y_fit_lower 是上面生成的拟合结果
# x_fit = np.linspace(0, 1600, 1600)
# y_fit_upper = np.polyval([0.02, 0.3, 5], x_fit)  # 例子：用拟合公式生成上界
# y_fit_lower = np.polyval([0.02, 0.1, 1], x_fit)  # 例子：用拟合公式生成下界

# 将 x_fit 映射到图像的横坐标范围（假设图像的宽度是 512）
x_img = np.linspace(0, image_shape[1] - 1, len(x_fit))

# 1. 遍历每个 x_img 值，找到对应的上界和下界 y 值
for i, x in enumerate(x_img):
    # 确保 y 值在图像范围内
    y_upper = int(np.clip(y_fit_upper[i], 0, image_shape[0] - 1))
    y_lower = int(np.clip(y_fit_lower[i], 0, image_shape[0] - 1))

    # 2. 将位于 [y_lower, y_upper] 范围内的像素值设为 1
    mask[y_lower:y_upper, int(x)] = 1

# 3. 可视化生成的掩码
plt.figure(figsize=(6, 6))
plt.imshow(mask, cmap='gray')
plt.title('Mask for the Fitted Band Region')
plt.show()


mask.shape



fig, ax = plt.subplots(1, 1, figsize=(9, 16))
fig.canvas.set_window_title('Point cloud in camera view')

ax.imshow(im_p_b)
coloring = coloring_p_b[mask_p_b]
dot_size = 5
ax.scatter(pp[0, :], pp[1, :], c=coloring, s=dot_size)
plt.scatter(pp[0,:], pp[1,:], s=10, label='Points', alpha=0.5)

ax.axis('on')

def plot_point_in_camview(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[0, :], points[1, :], c=coloring, s=dot_size)
    ax.axis('off')
    return fig, ax

import cv2
import numpy as np
from matplotlib import pyplot as plt

def fit_box_cv(points):
    # calculate the box
    rect = cv2.minAreaRect(points.astype(np.float32))  # Convert data type to CV_32F
    box = cv2.boxPoints(rect)
    box = np.intp(box)
    return box

def draw_polar_boundary(points):
    box = fit_box_cv(points)
    # find the right side points
    # right_side = sorted(box, key=lambda p: p[0], reverse=True)[:2] # box points sorted clockwisely
    sorted_by_x = sorted(box, key=lambda p: p[0])
    left_side = sorted_by_x[:2]  # x 坐标最小的两个点
    left_midpoint = np.mean(left_side, axis=0)  # 计算左侧边的中点

    # 找出 x 坐标最大的两个顶点（即右侧的边）
    right_side = sorted_by_x[-2:]  # x 坐标最大的两个点
    right_midpoint = np.mean(right_side, axis=0)  # 计算右侧边的中点
    return left_midpoint[0], right_midpoint[0] # cut by x axis

def merge_images_right(im1, im2, r, H=1600):
    """merge the mixed image to the right side of the original image"""
    assert im1.shape == im2.shape
    new = np.array(im1).copy()
    swap = np.array(im2).copy()
    new[:, int(r):] = swap[:, int(r):]
    return new

def draw_dashed_line(img, pt1, pt2, color, thickness=2, dash_length=10):
    # 计算点之间的距离
    dist = np.linalg.norm(np.array(pt2) - np.array(pt1))
    
    # 计算两个点之间的方向向量
    dashes = np.linspace(0, dist, int(dist / dash_length))
    
    # 在两个点之间绘制虚线段
    for i in range(0, len(dashes) - 1, 2):
        # 计算每段线段的起点和终点
        start = pt1 + (pt2 - pt1) * (dashes[i] / dist)
        end = pt1 + (pt2 - pt1) * (dashes[i + 1] / dist)
        
        # 将起点和终点的坐标转换为整数
        start = tuple(start.astype(int))
        end = tuple(end.astype(int))
        
        # 画短线段
        img = cv2.line(img, start, end, color, thickness)
    return img

def draw_dashed_box(img, box, color=(0, 0, 0), thickness=2, dash_length=10):
    """
    绘制虚线框的方法
    :param img: 图像
    :param box: 矩形的四个顶点坐标
    :param color: 颜色
    :param thickness: 线条厚度
    :param dash_length: 每个虚线段的长度
    """
    # 绘制矩形的四条边
    for i in range(4):
        pt1 = box[i]
        pt2 = box[(i + 1) % 4]  # 每次连接当前点和下一个点，最后一条边连接到第一个点
        img = draw_dashed_line(img, pt1, pt2, color, thickness, dash_length)
    return img

def get_Image_from_token(nusc, token):
    cam = nusc.get('sample_data', token)
    im = Image.open(osp.join(nusc.dataroot, cam['filename']))
    return im

def merge_images_yaw(im1, im2, l, r):
    """merge the mixed image to the right side of the original image"""
    assert im1.shape == im2.shape
    assert l <= r
    print(l,r)
    l, r = int(l), int(r)
    W = im1.shape[1]
    
    new = np.array(im2).copy()
    orig = np.array(im1).copy()
    
    new[:, l:r, :] = orig[:, l:r, :]
    return new

s1, s2 = 12,66
v = 2
view = VIEWS[v]

angle = .5-np.pi

input_dict_1 = input_dicts[s1].copy()
sample_token_v1 = data['data_list'][s1]['token']
calib_t1, ego_t1, cam_t1 = get_view_token(data['data_list'], s1, view=view)
input_dict_2 = input_dicts[s2].copy()
sample_token_v2 = data['data_list'][s2]['token']
calib_t2, ego_t2, cam_t2 = get_view_token(data['data_list'], s2, view=view)
input_dict_1['mix_results'] = input_dict_2
input_dict_1['angle'] = angle

aug_polarmix = _PolarMix(instance_classes=[1,2,3,4,5,6,7,8,9], swap_ratio=1.0, rotate_paste_ratio=0, pre_transform=None, prob=1.0) # [9,14,15,16,17,18,21,2,3,4,6,12,22]
out_dict = aug_polarmix(input_dict_1)


point_coor = out_dict['points'].coord.clone()
lidar2img  = out_dict['lidar2img']
sem_mask_   = out_dict['pts_semantic_mask'].copy()

for i in range(6):
    im_new = out_dict['images'][i]
    points_img, mask = proj_lidar2img(point_coor, lidar2img[VIEWS[i]], img_size=(1600, 900), min_dist=1.0)
    sem_mask_ = out_dict['pts_semantic_mask'].copy()[mask]
    coloring = np.array([color_map[i] for i in sem_mask_])/255
    plot_point_in_camview_2(points_img.numpy(), coloring, im_new[:,:,[2,1,0]], dot_size=5)

# from tools.projection.pc2img import map_pointcloud_to_image_2
# from tools.projection.pc2img import map_pointcloud_to_image, map_pointcloud_to_image_mask, map_pointcloud_to_image_2, voxelidx2polarcoor, polar2cartesian, save_render

### load point cloud from pkl file
point_coor = out_dict['points'].coord
# point_coor = torch.cat((point_coor, torch.ones((point_coor.shape[0], 1))), axis=1)

### load semantic mask
sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
binary_mask = out_dict['binary_mask'].numpy()
bin_mask    = binary_mask == 1

lidar2img_1 = input_dict_1['lidar2img']
lidar2img_2 = input_dict_1['mix_results']['lidar2img']
im_orig, im_mix = input_dict_1['images'][v], input_dict_1['mix_results']['images'][v]

point_coor_block = point_coor[bin_mask]
points_img, mask = proj_lidar2img(point_coor_block, lidar2img_1[view], img_size=(1600, 900), min_dist=1.0)
sem_mask = sem_mask[bin_mask][mask]
coloring = np.array([color_map[i] for i in sem_mask])/255 

################### IMAGE FUSION #######################
l, r = draw_polar_boundary(points_img.numpy())

# im_1_np, im_2_np = np.array(im_new), np.array(im_2)
new = merge_images_yaw(im_orig, im_mix, l, r)
# # draw a dashed line
new = draw_dashed_line(new, np.array([l, 0]), np.array([l, 900]), (0,0,0), thickness=3, dash_length=15)
new = draw_dashed_line(new, np.array([r, 0]), np.array([r, 900]), (0,0,0), thickness=3, dash_length=15)
# ################### IMAGE FUSION #######################

plot_point_in_camview_2(points_img.numpy(), coloring, new, dot_size=5)

# box
def crop_box_img(box, img):
    assert isinstance(img, np.ndarray)
    assert box.min() >= 0
    x, y, w, h = cv2.boundingRect(box)
    cropped_img = img[y:y+h, x:x+w]
    return cropped_img
# plt.imshow(crop_box_img(box_ex, im_2_np))
def paste_box_img(box, source_img, target_img):
    assert isinstance(source_img, np.ndarray)
    assert isinstance(target_img, np.ndarray)
    assert box.min() >= 0
    x, y, w, h = cv2.boundingRect(box)
    assert source_img[y:y+h, x:x+w].shape == target_img.shape
    source_img[y:y+h, x:x+w] = target_img
    return source_img

import cv2
import numpy as np

def expand_box(box, x_expand, image_shape):
    # 假设 box 是由 cv2.boxPoints(rect) 生成的四个顶点
    # image_shape 是图像的形状 (height, width)

    # 获取图像的高度和宽度
    img_height, img_width = image_shape[:2]
    
    # 获取 box 的左、右、上、下边界的 x 和 y 坐标
    x_coords = box[:, 0]
    y_coords = box[:, 1]

    # 分别拓展四个边界
    min_x = max(np.min(x_coords) - x_expand, 0)  # 左边界拓展
    max_x = min(np.max(x_coords) + x_expand, img_width - 1)  # 右边界拓展
    min_y = max(np.min(y_coords) - x_expand, 0)  # 上边界拓展
    max_y = min(np.max(y_coords) + x_expand, img_height - 1)  # 下边界拓展

    # 生成拓展后的矩形四个顶点 (假设是轴对齐矩形)
    expanded_box = np.array([[min_x, min_y],
                             [max_x, min_y],
                             [max_x, max_y],
                             [min_x, max_y]], dtype=np.int32)
    
    return expanded_box

def expand_box_proportional(box, scale, image_shape):
    """
    根据最长边按比例扩大矩形，并保持旋转角度，确保在图像范围内。

    参数：
    box: 矩形的四个顶点
    scale: 扩大的比例（例如 1.2 表示扩大 20%）
    image_shape: 图像的大小 (height, width)

    返回：
    扩展后的矩形四个顶点
    """
    # 获取图像的高度和宽度
    img_height, img_width = image_shape[:2]
    
    # 计算矩形中心点
    center = np.mean(box, axis=0)
    
    # 计算最长边
    def distance(pt1, pt2):
        return np.sqrt((pt1[0] - pt2[0])**2 + (pt1[1] - pt2[1])**2)
    
    # 计算所有边的长度
    d1 = distance(box[0], box[1])
    d2 = distance(box[1], box[2])
    d3 = distance(box[2], box[3])
    d4 = distance(box[3], box[0])
    
    # 找到最长的边
    max_length = max(d1, d2, d3, d4)
    
    # 按比例扩大
    expanded_box = []
    for point in box:
        # 计算从中心点到每个顶点的向量
        vector = point - center
        # 扩大向量长度
        expanded_vector = vector * scale
        # 计算新的顶点位置
        new_point = center + expanded_vector
        # 确保新顶点在图像范围内
        new_point[0] = np.clip(new_point[0], 0, img_width - 1)
        new_point[1] = np.clip(new_point[1], 0, img_height - 1)
        expanded_box.append(new_point)
    
    # 返回扩展后的矩形顶点
    return np.array(expanded_box, dtype=np.int32)

# 示例矩形顶点（假设通过 cv2.boxPoints(rect) 得到）
box = np.array([[100, 150], [200, 150], [200, 250], [100, 250]], dtype=np.float32)

# 图像大小
image_shape = (400, 400)

# 按比例扩大矩形，假设按 1.5 倍比例扩大
expanded_box = expand_box_proportional(box, scale=1.5, image_shape=image_shape)

# 画出原始和扩展后的矩形
image = np.zeros(image_shape + (3,), dtype=np.uint8)

# 画原始矩形
cv2.polylines(image, [box.astype(np.int32)], isClosed=True, color=(0, 255, 0), thickness=2)

# 画扩展后的矩形
cv2.polylines(image, [expanded_box], isClosed=True, color=(255, 0, 0), thickness=2)

# 显示结果
# cv2.imshow("Expanded Box", image)
# cv2.waitKey(0)
# cv2.destroyAllWindows()
plt.imshow(image)

s1, s2 = 5, 120
view = 'CAM_BACK_RIGHT'
angle = 0.05

input_dict_1 = input_dicts[s1].copy()
sample_token_v1 = data['data_list'][s1]['token']
calib_t1, ego_t1, cam_t1 = get_view_token(data['data_list'], s1, view=view)
input_dict_2 = input_dicts[s2].copy()
sample_token_v2 = data['data_list'][s2]['token']
calib_t2, ego_t2, cam_t2 = get_view_token(data['data_list'], s2, view=view)
input_dict_1['mix_results'] = input_dict_2
input_dict_1['angle'] = angle

aug_polarmix = _PolarMix(instance_classes=[1,2,3,4,5,6,7,8,9], swap_ratio=0, rotate_paste_ratio=1.0, pre_transform=None, prob=1.0) # [9,14,15,16,17,18,21,2,3,4,6,12,22]
out_dict = aug_polarmix(input_dict_1)


# valid the image output 
point_coor = out_dict['points'].coord.clone()
sem_mask = out_dict['pts_semantic_mask'].copy()

for v in range(6):
    im_new = out_dict['images'][v]
    points_img, mask = proj_lidar2img(point_coor, lidar2img[VIEWS[v]], img_size=(1600, 900), min_dist=1.0)
    sem_mask_ = sem_mask[mask]
    coloring = np.array([color_map[i] for i in sem_mask_])/255
    plot_point_in_camview_2(points_img.numpy(), coloring, im_new[:,:,[2,1,0]], dot_size=5)
    

# new version: current only support instance-copy
point_coor = out_dict['points'].coord.clone()
sem_mask = out_dict['pts_semantic_mask'] 
binary_mask = out_dict['binary_mask'].numpy()
lidar2img  = out_dict['lidar2img']
# pts_instance_mask = out_dict['pts_instance_mask']

cp_inst = torch.cat([inst.coord for inst in out_dict['block_cp']]).numpy()
cp_instlabel = np.concatenate(out_dict['block_cp_instlabel']) # at least know the number of cp
cp_semlabel  = np.concatenate(out_dict['block_cp_semlabel'])
N = binary_mask.shape[0]
N_cp = cp_instlabel.shape[0]
N_ro = 0

cp_mask = np.zeros(binary_mask.shape).astype(np.bool_)
cp_mask[N-(N_cp+N_ro):N-N_ro] = 1

for i in range(6):
    im_orig = input_dict_1['images'][i].numpy()
    # im_orig = out_dict['images'][i]
#     points_img, mask = proj_lidar2img(point_coor, lidar2img[VIEWS[i]], img_size=(1600, 900), min_dist=1.0)
#     sem_mask_ = out_dict['pts_semantic_mask'].copy()[mask]
#     coloring = np.array([color_map[i] for i in sem_mask_])/255
#     plot_point_in_camview_2(points_img.numpy(), coloring, im_new[:,:,[2,1,0]], dot_size=5)

    cp_coord, mask = proj_lidar2img(point_coor[cp_mask], lidar2img[VIEWS[i]], img_size=(1600, 900), min_dist=1.0)
    # sem_mask_ = out_dict['pts_semantic_mask'].copy()[mask]

    cp_coord = cp_coord.numpy()
    cp_instlabel_v = cp_instlabel[mask]
    cp_semlabel_v  = cp_semlabel[mask]
    coloring = np.array([color_map[i] for i in cp_semlabel_v])/255

    for inst_id in np.unique(cp_instlabel_v):
        inst_coord = cp_coord[cp_instlabel_v == inst_id]
        inst_cls = cp_semlabel_v[cp_instlabel_v == inst_id][0]
        # inst_coord = inst_coord[:2, :].T
        box = fit_box_cv(inst_coord)
        ##1.  expand the box
        # box = expand_box_proportional(box, 0.2, im_2_np.shape[:2])
        box_ex = expand_box(box, 50, im_2_np.shape[:2])
        ##2.  crop
        crop_img = crop_box_img(box_ex, im_2_np)
        crop_img_list.append(crop_img)
        ##3.  draw the box
        im_1_copy = draw_dashed_box(im_orig, box_ex, thickness=10, dash_length=20)
        # paste to orig image
        im_1_copy = paste_box_img(box_ex, im_1_copy, crop_img)

        # plt.imshow(im_1_copy)  
        # plt.axis('off')
        # plt.show()
    plot_point_in_camview_2(cp_coord, coloring, im_1_copy, dot_size=3)



# old version
point_coor = out_dict['points'].coord.numpy()
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)
sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
binary_mask = out_dict['binary_mask'].numpy()
add_instance_mask = out_dict['block_cp_instlabel']
bin_mask = binary_mask == 1

point_coord, coloring, im, mask = map_pointcloud_to_image_2( # new points: including original and mixed points
    nusc, point_coor,
    calib_t1, ego_t1, cam_t1,
    min_dist=1.0, 
    return_img=True,
    sem_mask=bin_mask,
    use_label_map=False)

im_2 = get_Image_from_token(nusc, cam_t2)

####################### Copy-Paste Instance Image #########################
im_1_np = np.array(im)
im_2_np = np.array(im_2)
im_1_copy = im_1_np.copy()
im_2_copy = im_2_np.copy()
crop_img_list = []

# # 1. get all added instances-> only works when no swap
# pts_instance_mask = out_dict['pts_instance_mask']
# new_insts_mask = ~binary_mask.astype(np.bool_) & mask
# for inst_id in np.unique(pts_instance_mask[new_insts_mask]):
#     inst_coord = point_coord[:, pts_instance_mask == inst_id]
#     inst_cls = sem_mask[pts_instance_mask == inst_id][0]

cp_inst = out_dict['block_cp']
cp_inst = torch.cat([inst.coord for inst in cp_inst])
cp_inst = cp_inst.numpy()
cp_instlabel = out_dict['block_cp_instlabel']
cp_instlabel = np.concatenate(cp_instlabel) # at least know the number of cp
cp_semlabel  = out_dict['block_cp_semlabel']

# ro_inst = out_dict['block_ro']
# ro_instlabel = out_dict['block_ro_instlabel']
# ro_inst = torch.cat([inst.coord for inst in ro_inst])
# ro_instlabel = np.concatenate(ro_instlabel) # at least know the number of ro

cp_cnt = cp_instlabel.shape[0]
ro_cnt = 0
# ro_cnt = ro_instlabel.shape[0]

cp_semlabel  = np.concatenate(cp_semlabel)
cp_mask = np.zeros(binary_mask.shape)
N = cp_mask.shape[0]
cp_mask[N-(cp_cnt+ro_cnt):N-ro_cnt] = 1
cp_mask_ = cp_mask.astype(np.bool_) & mask

pts_instance_mask = out_dict['pts_instance_mask']
cp_coord = point_coord[:, cp_mask_>0]
cp_instlabel = pts_instance_mask[cp_mask_>0]
cp_semlabel = sem_mask[cp_mask_>0]

# cp_mask_ = cp_mask_[cp_mask>0]
# cp_inst = cp_inst[cp_mask_>0]
# cp_instlabel = cp_instlabel[cp_mask_>0]
# cp_semlabel = cp_semlabel[cp_mask_>0]



for inst_id in np.unique(cp_instlabel):
    inst_coord = cp_coord[:, cp_instlabel == inst_id]
    inst_cls = cp_semlabel[cp_instlabel == inst_id][0]
    # insts.append((inst_coord, inst_cls))
    inst_coord = inst_coord[:2, :].T
    box = fit_box_cv(inst_coord)
    # expand the box
    # box = expand_box_proportional(box, 0.2, im_2_np.shape[:2])
    box_ex = expand_box(box, 50, im_2_np.shape[:2])
    # crop
    crop_img = crop_box_img(box_ex, im_2_np)
    crop_img_list.append(crop_img)
    #  draw the box
    im_1_copy = draw_dashed_box(im_1_copy, box_ex, thickness=10, dash_length=20)
    # im_1_copy = cv2.drawContours(im_1_copy, [box], 0, (255, 255, 0), 2)
    # im_1_copy = cv2.drawContours(im_1_copy, [box_ex], 0, (0, 255, 0), 2)
    # im_2_copy = cv2.drawContours(im_2_copy, [box], 0, (255, 255, 0), 2)
    # im_2_copy = cv2.drawContours(im_2_copy, [box_ex], 0, (0, 255, 0), 2)
    # paste to orig image
    im_1_copy = paste_box_img(box_ex, im_1_copy, crop_img)

    # plt.imshow(im_1_copy)  
    # plt.axis('off')
    # plt.show()
plot_point_in_camview(point_coord[:, mask], coloring[mask], im_1_copy, dot_size=3)

cp_cnt = cp_instlabel.shape[0]
ro_cnt = 0
cp_mask = np.zeros(binary_mask.shape)
N = cp_mask.shape[0]
cp_mask[N-(cp_cnt+ro_cnt):N-ro_cnt] = 1
np.count_nonzero(cp_mask), -(cp_cnt+ro_cnt),-ro_cnt





# visualize the copy-paste instance
add_instances = out_dict['block_cp']
add_instances = torch.cat([inst.coord for inst in add_instances])
add_instance_mask = out_dict['block_cp_instlabel']
add_instance_mask = np.concatenate(add_instance_mask)
add_instances.shape, add_instance_mask.shape, np.unique(add_instance_mask).shape

# plot copied instance
cp_coord = torch.cat([add_instances, torch.ones((add_instances.shape[0], 1))], dim=1).numpy()

# plot_point_in_camview(point_coord[:, mask], coloring[mask], im_1_copy, dot_size=3)
point_cp, coloring_cp, im_cp, mask_cp = map_pointcloud_to_image_2( # new points: including original and mixed points
    nusc, cp_coord,
    calib_t1, ego_t1, cam_t1,
    min_dist=1.0, 
    return_img=True,
    sem_mask=np.ones(add_instances.shape[0]),
    use_label_map=False)

plot_point_in_camview(point_cp[:, mask_cp], coloring_cp[mask_cp], im_cp, dot_size=3)

# visualize the rotated instance
s1 = 2
view = 'CAM_FRONT'

input_dict_1 = input_dicts[s1].copy()
sample_token_v1 = data['data_list'][s1]['token']
calib_t1, ego_t1, cam_t1 = get_view_token(data['data_list'], s1, view=view)

ro_inst = out_dict['block_ro']
ro_instlabel = out_dict['block_ro_instlabel']
ro_inst = torch.cat([inst.coord for inst in ro_inst])
ro_instlabel = np.concatenate(ro_instlabel)
# ro_inst.shape, ro_instlabel.shape, np.unique(ro_instlabel).shape

ro_coord = torch.cat([ro_inst, torch.ones((ro_inst.shape[0], 1))], dim=1).numpy()

# plot_point_in_camview(point_coord[:, mask], coloring[mask], im_1_copy, dot_size=3)
point_ro, coloring_ro, im_ro, mask_ro = map_pointcloud_to_image_2( # new points: including original and mixed points
    nusc, ro_coord,
    calib_t1, ego_t1, cam_t1,
    min_dist=1.0, 
    return_img=True,
    sem_mask=np.ones(ro_inst.shape[0]),
    use_label_map=False)

plot_point_in_camview(point_ro[:, mask_ro], coloring_ro[mask_ro], im_ro, dot_size=3)

ro_inst



pts_instance_mask = out_dict['pts_instance_mask']
insts = []
for inst_id in np.unique(pts_instance_mask[new_insts_mask]):
    inst_coord = point_coord[:, pts_instance_mask == inst_id]
    inst_cls = sem_mask[pts_instance_mask == inst_id][0]
    insts.append((inst_coord, inst_cls))
    

im_1_np = np.array(im)
im_2_np = np.array(im_2)
im_1_copy = im_1_np.copy()
im_2_copy = im_2_np.copy()
i = 1

for i in range(len(insts)):
    inst_coor = insts[i][0][:2, :].T
    box = fit_box_cv(inst_coor)
    # expand the box
    # box = expand_box_proportional(box, 0.2, im_2_np.shape[:2])
    box_ex = expand_box(box, 50, im_2_np.shape[:2])
    # crop
    crop_img = crop_box_img(box_ex, im_2_np)
    #  draw the box
    im_1_copy = draw_dashed_box(im_1_copy, box_ex, thickness=10, dash_length=20)
    # im_1_copy = cv2.drawContours(im_1_copy, [box], 0, (255, 255, 0), 2)
    # im_1_copy = cv2.drawContours(im_1_copy, [box_ex], 0, (0, 255, 0), 2)
    # im_2_copy = cv2.drawContours(im_2_copy, [box], 0, (255, 255, 0), 2)
    # im_2_copy = cv2.drawContours(im_2_copy, [box_ex], 0, (0, 255, 0), 2)
    # paste to orig image
    im_1_copy = paste_box_img(box_ex, im_1_copy, crop_img)

plt.imshow(im_1_copy)  
plt.axis('off')

plot_point_in_camview(point_coord[:, mask], coloring[mask], im_1_copy, dot_size=3)

s1 = 9
v = 0
view = VIEWS[0]

input_dict_1 = input_dicts[s1].copy()
lidar2img_1 = input_dicts[s1]['lidar2img']
point_coor = input_dict_1['points'].coord
sem_mask = input_dict_1['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
img = input_dict_1['images'][v].numpy()

img_scale = 0.4
w, h = img.shape[1], img.shape[0]
new_w, new_h = int(img.shape[1] * img_scale), int(img.shape[0] * img_scale)
resized_img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)

points_img, mask = proj_lidar2img(point_coor, lidar2img_1[view], img_size=(w, h), min_dist=1.0)
points_img = img_scale*points_img

sem_mask = sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

print(f'image resized to w: {new_w}, h:{new_h}')
plot_point_in_camview_2(points_img, coloring, resized_img, dot_size=5)

# LaserMix
s1, s2 = 18,119
v = 0
view = VIEWS[0]

input_dict_1 = input_dicts[s1].copy()
input_dict_2 = input_dicts[s2].copy()
input_dict_1['mix_results'] = input_dict_2

################# resize the image ##################
img = input_dict_1['images'][v].numpy()
img_scale = 0.4
w, h = img.shape[1], img.shape[0]
new_w, new_h = int(img.shape[1] * img_scale), int(img.shape[0] * img_scale)
input_dict_1['img_shape'] = (360, 640)
input_dict_1['ori_shape'] = (900, 1600)
input_dict_1['scale_factor'] = [0.4]

input_dict_1['images'] = [torch.tensor(cv2.resize(img.numpy(), (new_w, new_h), interpolation=cv2.INTER_LINEAR)) 
                          for img in input_dict_1['images']]
input_dict_1['mix_results']['images'] = [torch.tensor(cv2.resize(img.numpy(), (new_w, new_h), interpolation=cv2.INTER_LINEAR)) 
                                         for img in input_dict_1['mix_results']['images']]
################# resize the image ##################

aug_lasermix = _LaserMix(num_areas=[3], pitch_angles=[-30, 10], pre_transform=None, prob=1.0)
out_dict = aug_lasermix(input_dict_1)

point_coor = out_dict['points'].coord
pts_sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
new_im = out_dict['images'][v].numpy()
lidar2img = out_dict['lidar2img'][view]

points_img, mask = proj_lidar2img(point_coor, lidar2img, img_size=(w, h), min_dist=1.0)
points_img = img_scale*points_img
sem_mask = pts_sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

plot_point_in_camview_2(points_img, coloring, new_im, dot_size=5)

# PolarMix
s1, s2 = 22,132
v = 0
view = VIEWS[v]

input_dict_1 = input_dicts[s1].copy()
input_dict_2 = input_dicts[s2].copy()
input_dict_1['mix_results'] = input_dict_2

################# resize the image ##################
img = input_dict_1['images'][v].numpy()
img_scale = 0.4
w, h = img.shape[1], img.shape[0]
new_w, new_h = int(img.shape[1] * img_scale), int(img.shape[0] * img_scale)
input_dict_1['img_shape'] = (360, 640)
input_dict_1['ori_shape'] = (900, 1600)
input_dict_1['scale_factor'] = [0.4]

input_dict_1['images'] = [torch.tensor(cv2.resize(img.numpy(), (new_w, new_h), interpolation=cv2.INTER_LINEAR)) 
                          for img in input_dict_1['images']]
input_dict_1['mix_results']['images'] = [torch.tensor(cv2.resize(img.numpy(), (new_w, new_h), interpolation=cv2.INTER_LINEAR)) 
                                         for img in input_dict_1['mix_results']['images']]
################# resize the image ##################

aug_polarmix = _PolarMix(instance_classes=[1,2,3,4,5,6,7,8,9], swap_ratio=0, rotate_paste_ratio=0, pre_transform=None, prob=1.0) # [9,14,15,16,17,18,21,2,3,4,6,12,22]
out_dict = aug_polarmix(input_dict_1)

point_coor = out_dict['points'].coord
pts_sem_mask = out_dict['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
new_im = in['images'][v]
lidar2img = out_dict['lidar2img'][view]

points_img, mask = proj_lidar2img(point_coor, lidar2img, img_size=(w, h), min_dist=1.0)
points_img = img_scale*points_img
sem_mask = pts_sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

print(f'image resized to w: {new_w}, h:{new_h}')
plot_point_in_camview_2(points_img, coloring, new_im, dot_size=5)

point_coor = input_dicts[s1]['points'].coord
pts_sem_mask = input_dicts[s1]['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
lidar2img = input_dicts[s1]['lidar2img'][view]
img = input_dicts[s1]['images'][v].numpy()

points_img, mask = proj_lidar2img(point_coor, lidar2img, img_size=(w, h), min_dist=1.0)
# points_img = img_scale*points_img
sem_mask = pts_sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

print(f'image resized to w: {new_w}, h:{new_h}')
plot_point_in_camview_2(points_img, coloring, img, dot_size=5)

# plt.imshow(input_dicts[s1]['images'][v].numpy())
# plt.axis('off')
# plt.show()

plt.imshow(new_im)
plt.axis('off')
plt.show()

scale = 0.1
scale_mat = torch.tensor(
            [
                [scale, 0, 0, 0],
                [0, scale, 0, 0],
                [0, 0, scale, 0],
                [0, 0, 0, 1],
            ]
        )
scale_mat_inv = torch.inverse(scale_mat)

s1 = 193
v = 0
view = VIEWS[0]

input_dict_1 = input_dicts[s1].copy()
lidar2img_1 = input_dicts[s1]['lidar2img']
point_coor = input_dict_1['points'].coord
sem_mask = input_dict_1['pts_semantic_mask'] # res_list[i]['pts_semantic_mask']
img = input_dict_1['images'][v].numpy()

############### pts scale ###############
point_coor_scale = torch.cat([point_coor, torch.ones(point_coor.shape[0],1)], dim=1)
point_coor_scale = (point_coor_scale @ scale_mat)[:,:3]
lidar2img = lidar2img_1[view]
lidar2img_scale = lidar2img @ scale_mat_inv
############### pts scale ###############

points_img, mask = proj_lidar2img(point_coor_scale, lidar2img_scale, img_size=(w, h), min_dist=1.0)
sem_mask = sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

plot_point_in_camview_2(points_img, coloring, img, dot_size=5)

s1 = 5
v = 0
view = VIEWS[v]

input_dict_1 = input_dicts[s1].copy()
lidar2img_1 = input_dicts[s1]['lidar2img'][view].clone()
print('in', lidar2img_1)
if 'lidar_aug_matrix' in input_dict_1.keys():
    input_dict_1.pop('lidar_aug_matrix')
    
s2 = 198
input_dict_2 = input_dicts[s2].copy()
input_dict_1['mix_results'] = input_dict_2


aug_lasermix = _LaserMix(num_areas=[4], pitch_angles=[-30, 10], pre_transform=None, prob=1.0)
out_dict = aug_lasermix(input_dict_1)

################ Global Rotate Scale and Transform ##############
# ## v1. GlobalRotScaleTrans_MM
# translation_std = [0.1, 0.1, 0.1]
# input_dict_1['pcd_scale_factor'] = 2.
# input_dict_1['rot_axis'] = 1
# aug_ratscaletrans = GlobalRotScaleTrans_MM(translation_std=translation_std)
# out_dict = aug_ratscaletrans(input_dict_1)
# lidar_aug_matrix = out_dict['lidar_aug_matrix']
# lidar2img_out = lidar2img_1 @ np.linalg.inv(lidar_aug_matrix)
# lidar2img_out = lidar2img_out.float()
# v2. GlobalRotScaleTransAll
aug_ratscaletrans_cmt = GlobalRotScaleTransAll(translation_std=translation_std)
out_dict = aug_ratscaletrans_cmt(out_dict, rot_axis=2)

# print('out', lidar2img_out)
################ Global Rotate Scale and Transform ##############

lidar2img_out = out_dict['lidar2img'][view]
lidar2img_out = torch.tensor(lidar2img_out).float()


point_coor = out_dict['points'].coord.clone()
pts_sem_mask = out_dict['pts_semantic_mask'].copy() # res_list[i]['pts_semantic_mask']
img = out_dict['images'][v].numpy()
w, h = img.shape[1], img.shape[0]

points_img, mask = proj_lidar2img(point_coor, lidar2img_out, img_size=(w, h), min_dist=1.0)
sem_mask = pts_sem_mask[mask]
coloring = np.array([color_map[i] for i in sem_mask])/255

plot_point_in_camview_2(points_img, coloring, img, dot_size=5)

img.shape