import os
import sys
# sys.path.append(os.path.join(os.getcwd(), "/.."))
import numpy as np
import torch
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS
from typing import List, Optional, Sequence, Tuple, Union


class BEVFusionRandomFlip3D:
    """Compared with `RandomFlip3D`, this class directly records the lidar
    augmentation matrix in the `data`."""

    def __call__(self, data: Dict[str, Any]) -> Dict[str, Any]:
        flip_horizontal = np.random.choice([0, 1])
        flip_vertical = np.random.choice([0, 1])

        rotation = np.eye(3)
        if flip_horizontal:
            rotation = np.array([[1, 0, 0], [0, -1, 0], [0, 0, 1]]) @ rotation
            if 'points' in data:
                data['points'].flip('horizontal')
            if 'gt_bboxes_3d' in data:
                data['gt_bboxes_3d'].flip('horizontal')
            if 'gt_masks_bev' in data:
                data['gt_masks_bev'] = data['gt_masks_bev'][:, :, ::-1].copy()

        if flip_vertical:
            rotation = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, 1]]) @ rotation
            if 'points' in data:
                data['points'].flip('vertical')
            if 'gt_bboxes_3d' in data:
                data['gt_bboxes_3d'].flip('vertical')
            if 'gt_masks_bev' in data:
                data['gt_masks_bev'] = data['gt_masks_bev'][:, ::-1, :].copy()

        if 'lidar_aug_matrix' not in data:
            data['lidar_aug_matrix'] = np.eye(4)
        data['lidar_aug_matrix'][:3, :] = rotation @ data[
            'lidar_aug_matrix'][:3, :]
        return data


from mmdet3d.datasets import GlobalRotScaleTrans

@TRANSFORMS.register_module()
class BEVFusionGlobalRotScaleTrans(GlobalRotScaleTrans):
    """Compared with `GlobalRotScaleTrans`, the augmentation order in this
    class is rotation, translation and scaling (RTS)."""

    def transform(self, input_dict: dict) -> dict:
        """Private function to rotate, scale and translate bounding boxes and
        points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points', 'pcd_rotation',
            'pcd_scale_factor', 'pcd_trans' and `gt_bboxes_3d` are updated
            in the result dict.
        """
        # if 'transformation_3d_flow' not in input_dict:
        #     input_dict['transformation_3d_flow'] = []

        self._rot_bbox_points(input_dict)

        if 'pcd_scale_factor' not in input_dict:
            self._random_scale(input_dict)
        self._trans_bbox_points(input_dict)
        self._scale_bbox_points(input_dict)

        # input_dict['transformation_3d_flow'].extend(['R', 'T', 'S'])

        lidar_augs = np.eye(4)
        lidar_augs[:3, :3] = input_dict['pcd_rotation'].T * input_dict[
            'pcd_scale_factor']
        lidar_augs[:3, 3] = input_dict['pcd_trans'] * \
            input_dict['pcd_scale_factor']

        if 'lidar_aug_matrix' not in input_dict:
            input_dict['lidar_aug_matrix'] = np.eye(4)
        input_dict[
            'lidar_aug_matrix'] = lidar_augs @ input_dict['lidar_aug_matrix']

        return input_dict



# @PIPELINES.register_module()
class GlobalRotScaleTransAll(object):
    """Apply global rotation, scaling and translation to a 3D scene.

    Args:
        rot_range (list[float]): Range of rotation angle.
            Defaults to [-0.78539816, 0.78539816] (close to [-pi/4, pi/4]).
        scale_ratio_range (list[float]): Range of scale ratio.
            Defaults to [0.95, 1.05].
        translation_std (list[float]): The standard deviation of translation
            noise. This applies random translation to a scene by a noise, which
            is sampled from a gaussian distribution whose standard deviation
            is set by ``translation_std``. Defaults to [0, 0, 0]
        shift_height (bool): Whether to shift height.
            (the fourth dimension of indoor points) when scaling.
            Defaults to False.
    """

    def __init__(self,
                 rot_range=[-0.78539816, 0.78539816],
                 scale_ratio_range=[0.95, 1.05],
                 translation_std=[0, 0, 0],
                 shift_height=False):
        seq_types = (list, tuple, np.ndarray)
        if not isinstance(rot_range, seq_types):
            assert isinstance(rot_range, (int, float)), \
                f'unsupported rot_range type {type(rot_range)}'
            rot_range = [-rot_range, rot_range]
        self.rot_range = rot_range

        assert isinstance(scale_ratio_range, seq_types), \
            f'unsupported scale_ratio_range type {type(scale_ratio_range)}'
        self.scale_ratio_range = scale_ratio_range

        if not isinstance(translation_std, seq_types):
            assert isinstance(translation_std, (int, float)), \
                f'unsupported translation_std type {type(translation_std)}'
            translation_std = [
                translation_std, translation_std, translation_std
            ]
        assert all([std >= 0 for std in translation_std]), \
            'translation_std should be positive'
        self.translation_std = translation_std
        self.shift_height = shift_height
        self.VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']


    def _trans_bbox_points(self, input_dict):
        """Private function to translate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after translation, 'points', 'pcd_trans' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        translation_std = np.array(self.translation_std, dtype=np.float32)
        trans_factor = np.random.normal(scale=translation_std, size=3).T
        
        input_dict['points'].translate(trans_factor)
        if 'radar' in input_dict:
            input_dict['radar'].translate(trans_factor)
        input_dict['pcd_trans'] = trans_factor
        for key in input_dict['bbox3d_fields']:
            input_dict[key].translate(trans_factor)

        trans_mat = np.eye(4)
        trans_mat[:3, -1] = trans_factor
        trans_mat_inv = np.linalg.inv(trans_mat)
        for i in range(len(input_dict["lidar2img"])):
            view = self.VIEWS[i]
            input_dict["lidar2img"][view] = input_dict["lidar2img"][view] @ trans_mat_inv
            input_dict["lidar2cam"][view] = input_dict["lidar2cam"][view] @ trans_mat_inv

    def _rot_bbox_points(self, input_dict):
        """Private function to rotate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after rotation, 'points', 'pcd_rotation' \
                and keys in input_dict['bbox3d_fields'] are updated \
                in the result dict.
        """
        if 'rot_degree' not in input_dict:
            rotation = self.rot_range
            noise_rotation = np.random.uniform(rotation[0], rotation[1])
        else:
            noise_rotation = input_dict['rot_degree']

        # if no bbox in input_dict, only rotate points
        if len(input_dict['bbox3d_fields']) == 0:
            if 'rot_degree' not in input_dict:
                rot_mat_T = input_dict['points'].rotate(noise_rotation)
                if 'radar' in input_dict:
                    input_dict['radar'].rotate(noise_rotation)
            else:
                rot_mat_T = input_dict['points'].rotate(-noise_rotation)
                if 'radar' in input_dict:
                    input_dict['radar'].rotate(-noise_rotation)
            input_dict['pcd_rotation'] = rot_mat_T

            rot_mat = torch.eye(4)
            rot_mat[:3, :3].copy_(rot_mat_T)
            rot_mat[0, 1], rot_mat[1, 0] = -rot_mat[0, 1], -rot_mat[1, 0]
            rot_mat_inv = torch.inverse(rot_mat)
            for i in range(len(input_dict["lidar2img"])):
                view = self.VIEWS[i]
                input_dict["lidar2img"][view] = (torch.tensor(input_dict["lidar2img"][view]).float() @ rot_mat_inv).numpy()
                input_dict["lidar2cam"][view] = (torch.tensor(input_dict["lidar2cam"][view]).float() @ rot_mat_inv).numpy()
            return

        # rotate points with bboxes
        for key in input_dict['bbox3d_fields']:
            if len(input_dict[key].tensor) != 0:
                points, rot_mat_T = input_dict[key].rotate(
                    noise_rotation, input_dict['points'])
                input_dict['points'] = points
                input_dict['pcd_rotation'] = rot_mat_T
                if 'radar' in input_dict:
                    input_dict['radar'].rotate(-noise_rotation)

                rot_mat = torch.eye(4)
                rot_mat[:3, :3].copy_(rot_mat_T)
                rot_mat[0, 1], rot_mat[1, 0] = -rot_mat[0, 1], -rot_mat[1, 0]
                rot_mat_inv = torch.inverse(rot_mat)
                for view in range(len(input_dict["lidar2img"])):
                    input_dict["lidar2img"][view] = (torch.tensor(input_dict["lidar2img"][view]).float() @ rot_mat_inv).numpy()
                    input_dict["lidar2cam"][view] = (torch.tensor(input_dict["lidar2cam"][view]).float() @ rot_mat_inv).numpy()


    def _scale_bbox_points(self, input_dict):
        """Private function to scale bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points'and keys in \
                input_dict['bbox3d_fields'] are updated in the result dict.
        """
        scale = input_dict['pcd_scale_factor']
        points = input_dict['points']
        points.scale(scale)
        if self.shift_height:
            assert 'height' in points.attribute_dims.keys(), \
                'setting shift_height=True but points have no height attribute'
            points.tensor[:, points.attribute_dims['height']] *= scale
        input_dict['points'] = points
        
        if 'radar' in input_dict:
            input_dict['radar'].scale(scale)
            
        for key in input_dict['bbox3d_fields']:
            input_dict[key].scale(scale)

        scale_mat = torch.tensor(
            [
                [scale, 0, 0, 0],
                [0, scale, 0, 0],
                [0, 0, scale, 0],
                [0, 0, 0, 1],
            ]
        )
        scale_mat_inv = torch.inverse(scale_mat)
        for i in range(len(input_dict["lidar2img"])):
            view = self.VIEWS[i]
            input_dict["lidar2img"][view] = (torch.tensor(input_dict["lidar2img"][view]).float() @ scale_mat_inv).numpy()
            input_dict["lidar2cam"][view] = (torch.tensor(input_dict["lidar2cam"][view]).float() @ scale_mat_inv).numpy()

    def _random_scale(self, input_dict):
        """Private function to randomly set the scale factor.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'pcd_scale_factor' are updated \
                in the result dict.
        """
        scale_factor = np.random.uniform(self.scale_ratio_range[0],
                                         self.scale_ratio_range[1])
        input_dict['pcd_scale_factor'] = scale_factor

    def __call__(self, input_dict):
        """Private function to rotate, scale and translate bounding boxes and \
        points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points', 'pcd_rotation',
                'pcd_scale_factor', 'pcd_trans' and keys in \
                input_dict['bbox3d_fields'] are updated in the result dict.
        """
        if 'transformation_3d_flow' not in input_dict:
            input_dict['transformation_3d_flow'] = []

        self._rot_bbox_points(input_dict)

        if 'pcd_scale_factor' not in input_dict:
            self._random_scale(input_dict)
        self._scale_bbox_points(input_dict)

        self._trans_bbox_points(input_dict)

        input_dict['transformation_3d_flow'].extend(['R', 'S', 'T'])
        return input_dict

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(rot_range={self.rot_range},'
        repr_str += f' scale_ratio_range={self.scale_ratio_range},'
        repr_str += f' translation_std={self.translation_std},'
        repr_str += f' shift_height={self.shift_height})'
        return repr_str



# from datasets.transforms.transforms_3d import 

# @TRANSFORMS.register_module()
class GlobalRotScaleTrans(BaseTransform):
    """Apply global rotation, scaling and translation to a 3D scene.

    Required Keys:

    - points (np.float32)
    - gt_bboxes_3d (np.float32)

    Modified Keys:

    - points (np.float32)
    - gt_bboxes_3d (np.float32)

    Added Keys:

    - points (np.float32)
    - pcd_trans (np.float32)
    - pcd_rotation (np.float32)
    - pcd_rotation_angle (np.float32)
    - pcd_scale_factor (np.float32)

    Args:
        rot_range (list[float]): Range of rotation angle.
            Defaults to [-0.78539816, 0.78539816] (close to [-pi/4, pi/4]).
        scale_ratio_range (list[float]): Range of scale ratio.
            Defaults to [0.95, 1.05].
        translation_std (list[float]): The standard deviation of
            translation noise applied to a scene, which
            is sampled from a gaussian distribution whose standard deviation
            is set by ``translation_std``. Defaults to [0, 0, 0].
        shift_height (bool): Whether to shift height.
            (the fourth dimension of indoor points) when scaling.
            Defaults to False.
    """

    def __init__(self,
                 rot_range: List[float] = [-0.78539816, 0.78539816],
                 scale_ratio_range: List[float] = [0.95, 1.05],
                 translation_std: List[int] = [0, 0, 0],
                 shift_height: bool = False) -> None:
        seq_types = (list, tuple, np.ndarray)
        if not isinstance(rot_range, seq_types):
            assert isinstance(rot_range, (int, float)), \
                f'unsupported rot_range type {type(rot_range)}'
            rot_range = [-rot_range, rot_range]
        self.rot_range = rot_range

        assert isinstance(scale_ratio_range, seq_types), \
            f'unsupported scale_ratio_range type {type(scale_ratio_range)}'

        self.scale_ratio_range = scale_ratio_range

        if not isinstance(translation_std, seq_types):
            assert isinstance(translation_std, (int, float)), \
                f'unsupported translation_std type {type(translation_std)}'
            translation_std = [
                translation_std, translation_std, translation_std
            ]
        assert all([std >= 0 for std in translation_std]), \
            'translation_std should be positive'
        self.translation_std = translation_std
        self.shift_height = shift_height

    def _trans_bbox_points(self, input_dict: dict) -> None:
        """Private function to translate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after translation, 'points', 'pcd_trans'
            and `gt_bboxes_3d` is updated in the result dict.
        """
        translation_std = np.array(self.translation_std, dtype=np.float32)
        trans_factor = np.random.normal(scale=translation_std, size=3).T

        input_dict['points'].translate(trans_factor)
        input_dict['pcd_trans'] = trans_factor
        if 'gt_bboxes_3d' in input_dict:
            input_dict['gt_bboxes_3d'].translate(trans_factor)

    def _rot_bbox_points(self, input_dict: dict) -> None:             
        """Private function to rotate bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after rotation, 'points', 'pcd_rotation'
            and `gt_bboxes_3d` is updated in the result dict.
        """
        rotation = self.rot_range
        noise_rotation = np.random.uniform(rotation[0], rotation[1])

        if 'gt_bboxes_3d' in input_dict and \
                len(input_dict['gt_bboxes_3d'].tensor) != 0:
            # rotate points with bboxes
            points, rot_mat_T = input_dict['gt_bboxes_3d'].rotate(
                noise_rotation, input_dict['points'])
            input_dict['points'] = points
        else:
            # if no bbox in input_dict, only rotate points
            rot_mat_T = input_dict['points'].rotate(noise_rotation)

        input_dict['pcd_rotation'] = rot_mat_T
        input_dict['pcd_rotation_angle'] = noise_rotation

    def _scale_bbox_points(self, input_dict: dict) -> None:
        """Private function to scale bounding boxes and points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points' and
            `gt_bboxes_3d` is updated in the result dict.
        """
        scale = input_dict['pcd_scale_factor']
        points = input_dict['points']
        points.scale(scale)
        if self.shift_height:
            assert 'height' in points.attribute_dims.keys(), \
                'setting shift_height=True but points have no height attribute'
            points.tensor[:, points.attribute_dims['height']] *= scale
        input_dict['points'] = points

        if 'gt_bboxes_3d' in input_dict and \
                len(input_dict['gt_bboxes_3d'].tensor) != 0:
            input_dict['gt_bboxes_3d'].scale(scale)

    def _random_scale(self, input_dict: dict) -> None:
        """Private function to randomly set the scale factor.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'pcd_scale_factor'
            are updated in the result dict.
        """
        scale_factor = np.random.uniform(self.scale_ratio_range[0],
                                         self.scale_ratio_range[1])
        input_dict['pcd_scale_factor'] = scale_factor

    def transform(self, input_dict: dict) -> dict:
        """Private function to rotate, scale and translate bounding boxes and
        points.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after scaling, 'points', 'pcd_rotation',
            'pcd_scale_factor', 'pcd_trans' and `gt_bboxes_3d` are updated
            in the result dict.
        """
        if 'transformation_3d_flow' not in input_dict:
            input_dict['transformation_3d_flow'] = []

        self._rot_bbox_points(input_dict)

        if 'pcd_scale_factor' not in input_dict:
            self._random_scale(input_dict)
        self._scale_bbox_points(input_dict)

        self._trans_bbox_points(input_dict)

        input_dict['transformation_3d_flow'].extend(['R', 'S', 'T'])
        return input_dict

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(rot_range={self.rot_range},'
        repr_str += f' scale_ratio_range={self.scale_ratio_range},'
        repr_str += f' translation_std={self.translation_std},'
        repr_str += f' shift_height={self.shift_height})'
        return repr_str



from nuscenes.nuscenes import NuScenes
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion
import numpy as np

dataroot = '/mnt/data/data/yining/codefield/3dmmpano_240109/data/nuscenes_mini'
nusc = NuScenes(version='v1.0-mini', dataroot=dataroot, verbose=True)

# 3. when sample_token is given，get the sample info
sample_token_v = 'ca9a282c9e77460f8360f564131a8af5'

sample_v = nusc.get('sample', sample_token_v)
pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
cam_token_v = sample_v['data']['CAM_FRONT']



return_img = True
points_data = np.eye(4)
point_calibrated_sensor_token = point_calibrated_sensor_token_v
point_ego_pose_token = point_ego_pose_token_v
camera_token = cam_token_v
min_dist = 1.0


img_H, img_W = 900, 1600 
cam = nusc.get('sample_data', camera_token) 
pc = _LidarPointCloud.pack_points(points_data.copy())
    
# Points live in the point sensor frame. So they need to be transformed via global to the image plane.
# First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
pc.translate(np.array(cs_record['translation']))

# Second step: transform from ego to the global frame.
poserecord = nusc.get('ego_pose', point_ego_pose_token)
pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
pc.translate(np.array(poserecord['translation']))

# Third step: transform from global into the ego vehicle frame for the timestamp of the image.
poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
pc.translate(-np.array(poserecord['translation']))
pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

# Fourth step: transform from ego into the camera.
cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
pc.translate(-np.array(cs_record['translation']))
pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)

# depths = pc.points[2, :]
# if return_img:
#     im = Image.open(osp.join(nusc.dataroot, cam['filename']))

#     # Fifth step: actually take a "picture" of the point cloud.
#     # Grab the depths (camera frame z axis points away from the camera).
#     # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
#     coloring = depths

# # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
# points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
    

# # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
# # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
# # casing for non-keyframes which are slightly out of sync.
# mask = np.ones(depths.shape[0], dtype=bool)
# mask = np.logical_and(mask, depths > min_dist)
# mask = np.logical_and(mask, points[0, :] > 1)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             , :] > 1)
# mask = np.logical_and(mask, points[0, :] < img_W - 1)
# mask = np.logical_and(mask, points[1, :] > 1)
# mask = np.logical_and(mask, points[1, :] < img_H - 1)
# # points = points[:, mask]
    
# # if return_img:
# #     # coloring = coloring[mask]
# #     return points, coloring, im, mask
# # else:
# #     return points, mask

    


pc.points
# invert pc.points
inv_l2c = np.linalg.inv(pc.points)
pc.points, inv_l2c

import h5py
import numpy as np
import os
import sys
import pickle

proj_path = '/mnt/data/data/yining/codefield/3dmmpano_240109'
sys.path.append(proj_path)

pklfile = os.path.join(proj_path, 'data/nuscenes_mini/nuscenes_infos_train_1.pkl')
# save_path = os.path.join(proj_path, 'data/nuscenes')



with open(pklfile, 'rb') as f:
    data = pickle.load(f)

view = data['data_list'][0]['images']['CAM_FRONT']
lidar2cam = view['lidar2cam']
cam2img = view['cam2img']
# lidar2cam, cam2img

data['data_list'][0].keys()

def load_point(pts_filename, backend_args=None, load_dim=5, use_dim=range(3)):
    from mmengine.fileio import get
    pts_bytes = get(pts_filename, backend_args=backend_args)
    points = np.frombuffer(pts_bytes, dtype=np.float32)
    points = points.reshape(-1, load_dim)
    points = points[:, use_dim]
    return points

def load_mask(pts_panoptic_mask_path, backend_args=None, seg_3d_dtype=np.int64, seg_offset=1000):
    from mmengine.fileio import get
    # pts_panoptic_mask_path = get(pts_panoptic_mask_path, backend_args=backend_args)
    # pts_panoptic_mask = np.frombuffer(mask_bytes, dtype=seg_3d_dtype).copy()
    # add .copy() to fix read-only bug
    # pts_panoptic_mask = np.frombuffer(mask_bytes, dtype=seg_3d_dtype).copy()
    # pts_semantic_mask = pts_semantic_mask // seg_offset
    # return pts_semantic_mask, pts_panoptic_mask
    pts_panoptic_mask = np.load(pts_panoptic_mask_path) 
    
    pts_panoptic_mask = pts_panoptic_mask['data']
    pts_semantic_mask = pts_panoptic_mask // seg_offset
    pts_instance_mask = pts_panoptic_mask.astype(seg_3d_dtype)
    return pts_semantic_mask, pts_instance_mask


def load_viewimages(filenames, backend_args=None, color_type='unchanged'):
    from mmengine.fileio import get
    import mmcv
    
    imgs = {}
    for view in VIEWS:
        # imgs[view] = {}
        img_byte = get(filenames[view], backend_args=backend_args)
        img = mmcv.imfrombytes(img_byte, flag=color_type) 
        imgs[view] = img
    return imgs

def load_viewimages_meta(filenames, backend_args=None, color_type='unchanged'):
    from mmengine.fileio import get
    import mmcv
    
    imgs = {}
    for view in VIEWS:
        imgs[view] = {}
        # load image 
        img_byte = get(filenames[view], backend_args=backend_args)
        img = mmcv.imfrombytes(img_byte, flag=color_type) 
        imgs[view]['img'] = img
        # load image meta
        
        
    return imgs

info = data['data_list'][0]
VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
data_prefix = {
    'dataset_path': 'data/nuscenes_mini',
    'lidar_path': 'data/nuscenes_mini/samples/LIDAR_TOP',
    'pts_panoptic_mask': 'data/nuscenes_mini/panoptic/v1.0-mini',
    'CAM_FRONT': 'data/nuscenes_mini/samples/CAM_FRONT',
    'CAM_FRONT_RIGHT': 'data/nuscenes_mini/samples/CAM_FRONT_RIGHT',
    'CAM_FRONT_LEFT': 'data/nuscenes_mini/samples/CAM_FRONT_LEFT',
    'CAM_BACK': 'data/nuscenes_mini/samples/CAM_BACK',
    'CAM_BACK_LEFT': 'data/nuscenes_mini/samples/CAM_BACK_LEFT',
    'CAM_BACK_RIGHT': 'data/nuscenes_mini/samples/CAM_BACK_RIGHT',}

# load lidar points
lidar_prefix = data_prefix.get('lidar_path')
lidar_path = info['lidar_points']['lidar_path']
lidar_path = os.path.join(lidar_prefix, lidar_path)
lidar_points = load_point(lidar_path)

# panoptic_prefix = 'data/nuscenes_mini/panoptic/v1.0-mini' 
dataset_path = data_prefix.get('dataset_path')
panoptic_path = info['pts_panoptic_mask_path']
panoptic_path = os.path.join(dataset_path, panoptic_path)
pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
pts_semantic_mask.shape

img_files_path = {}
meta_info = {}
for cam_id, img_info in info['images'].items(): # add prefix to img_path
    print(cam_id, img_info)
    if 'img_path' in img_info:
        if cam_id in data_prefix:
            cam_prefix = data_prefix[cam_id]
    #     else:
    #         cam_prefix = data_prefix.get('img', '')
        img_info['img_path'] = os.path.join(cam_prefix,
                                        img_info['img_path'])
        img_files_path[cam_id] = img_info['img_path']
    meta_info[cam_id] = {}
    if 'lidar2cam' in img_info:
        meta_info[cam_id]['lidar2cam'] = np.array(img_info['lidar2cam'])
    if 'cam2img' in img_info:
        meta_info[cam_id]['cam2img'] = np.array(img_info['cam2img'])


imgs = load_viewimages(img_files_path)
imgs

data_root = 'data/nuscenes_mini'

def load_point_mask_viewimages(pklfile, data_root):
    info_list = pklfile['data_list']
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',}

    res_list = []
    for info in info_list:
        res = {}
        
        # load lidar points
        lidar_prefix = data_prefix.get('lidar_path')
        lidar_path = info['lidar_points']['lidar_path']
        lidar_path = os.path.join(lidar_prefix, lidar_path)
        lidar_point = load_point(lidar_path)
        res['lidar_point'] = lidar_point
        
        # load panoptic mask
        dataset_path = data_prefix.get('dataset_path')
        panoptic_path = info['pts_panoptic_mask_path']
        panoptic_path = os.path.join(dataset_path, panoptic_path)
        pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
        # pts_semantic_mask.shape
        res['pts_semantic_mask'] = pts_semantic_mask
        res['pts_instance_mask'] = pts_instance_mask
        
        # load multi-view images
        img_files_path = {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            if 'img_path' in img_info:
                if cam_id in data_prefix:
                    cam_prefix = data_prefix[cam_id]
                img_info['img_path'] = os.path.join(cam_prefix,
                                                img_info['img_path'])
                img_files_path[cam_id] = img_info['img_path']
        imgs = load_viewimages(img_files_path)
        
        # pack by view
        meta_info = {}
        meta_info['img'] = {}
        
        for view in VIEWS:
            meta_info['img'][view] = imgs[view]
            
        # load multi-view images meta
        meta_info['lidar2cam'], meta_info['cam2img'] = {}, {}
        for cam_id, img_info in info['images'].items(): # add prefix to img_path
            # meta_info[cam_id] = {}
            if 'lidar2cam' in img_info:
                meta_info['lidar2cam'][cam_id] = np.array(img_info['lidar2cam'])
            if 'cam2img' in img_info:
                meta_info['cam2img'][cam_id] = np.array(img_info['cam2img'])

        res['imgs_meta'] = meta_info
        res_list.append(res)
    return res_list

with open(pklfile, 'rb') as f:
    data = pickle.load(f)

res_list = load_point_mask_viewimages(data, data_root)

import numpy as np
import torch
from typing import List, Tuple

from nuscenes.nuscenes import NuScenes, NuScenesExplorer
from nuscenes.utils.data_classes import LidarPointCloud
from pyquaternion import Quaternion
from nuscenes.utils.geometry_utils import view_points

import os.path as osp
from PIL import Image
    
def map_pointcloud_to_image_mask(nusc,
                            points_data: np.ndarray,  # (4, N)
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
    
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
    # print('scaling points!')
    # points = points*0.4

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    

aug_ratscaletrans = GlobalRotScaleTransAll()

from mmdet3d.structures.points import BasePoints
from tools.projection.pc2img import get_lidar2img

for i in range(len(res_list)):
    point = res_list[i]['lidar_point']
    lidar2cams = res_list[i]['imgs_meta']['lidar2cam']
    cam2imgs = res_list[i]['imgs_meta']['cam2img']
    lidar2imgs = {}
    for i in range(len(lidar2cams)):
        view = VIEWS[i]
        lidar2img = get_lidar2img(torch.tensor(lidar2cams[view]).float(),  torch.tensor(cam2imgs[view]).float())
        lidar2imgs[view] = lidar2img
input_dict = {
    'points': BasePoints(point),
    'lidar2cam': lidar2cams,
    'cam2img': cam2imgs,
    'lidar2img': lidar2imgs,
    'bbox3d_fields': [],
    # 'transformation_3d_flow': [],
}

out_dict = aug_ratscaletrans(input_dict)

out_dict


def view_points(points: np.ndarray, view: np.ndarray, normalize: bool) -> np.ndarray:
    """
    This is a helper class that maps 3d points to a 2d plane. It can be used to implement both perspective and
    orthographic projections. It first applies the dot product between the points and the view. By convention,
    the view should be such that the data is projected onto the first 2 axis. It then optionally applies a
    normalization along the third dimension.

    For a perspective projection the view should be a 3x3 camera matrix, and normalize=True
    For an orthographic projection with translation the view is a 3x4 matrix and normalize=False
    For an orthographic projection without translation the view is a 3x3 matrix (optionally 3x4 with last columns
     all zeros) and normalize=False

    :param points: <np.float32: 3, n> Matrix of points, where each point (x, y, z) is along each column.
    :param view: <np.float32: n, n>. Defines an arbitrary projection (n <= 4).
        The projection should be such that the corners are projected onto the first 2 axis.
    :param normalize: Whether to normalize the remaining coordinate (along the third axis).
    :return: <np.float32: 3, n>. Mapped point. If normalize=False, the third coordinate is the height.
    """

    assert view.shape[0] <= 4
    assert view.shape[1] <= 4
    assert points.shape[0] == 3

    viewpad = np.eye(4)
    viewpad[:view.shape[0], :view.shape[1]] = view

    nbr_points = points.shape[1]

    # Do operation in homogenous coordinates.
    points = np.concatenate((points, np.ones((1, nbr_points))))
    points = np.dot(viewpad, points)
    points = points[:3, :]

    if normalize:
        points = points / points[2:3, :].repeat(3, 0).reshape(3, nbr_points)
        print(f'************** div {np.unique(points[2:3, :].repeat(3, 0).reshape(3, nbr_points))} **************')

    return points

img_H, img_W = 900, 1600
min_dist = 1.0
 
point_coor_torch = torch.tensor(point_coor_v).float()
# point_coor = input_dict['points'].tensor
# point_coor_torch = torch.cat((point_coor_torch, torch.ones((point_coor_torch.shape[0], 1))), axis=1)
nbr_points = point_coor_torch.shape[0]

view = 'CAM_FRONT'
lidar2img = input_dict['lidar2img'][view]
lidar2img = torch.tensor(lidar2img)

# points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
cam_coor = (lidar2img @ point_coor_torch.T)
cam_coor = cam_coor[:3, :]
cam_coor = cam_coor / cam_coor[2:3, :]
# cam_coor = cam_coor / cam_coor[2:3, :].repeat(3, 0).reshape(3, nbr_points)

depths = cam_coor[2, :]
# rewrite into torch
mask = torch.ones(nbr_points, dtype=bool)
mask = torch.logical_and(mask, depths > min_dist)
mask = torch.logical_and(mask, cam_coor[0, :] > 1)
mask = torch.logical_and(mask, cam_coor[0, :] < img_W - 1)
mask = torch.logical_and(mask, cam_coor[1, :] > 1)
mask = torch.logical_and(mask, cam_coor[1, :] < img_H - 1)

# mask = np.ones(nbr_points, dtype=bool)
# mask = np.logical_and(mask, depths > min_dist)
# mask = np.logical_and(mask, points[0, :] > 1)
# mask = np.logical_and(mask, points[0, :] < img_W - 1)
# mask = np.logical_and(mask, points[1, :] > 1)
# mask = np.logical_and(mask, points[1, :] < img_H - 1)

lidar2cam = input_dict['lidar2cam'][view]
cam2img = input_dict['cam2img'][view]
cam = nusc.get('sample_data', camera_token) 
cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
# lidar2cam, cam2img, cam_token_v, cs_record['camera_intrinsic']

# cam_coor_np = cam_coor.numpy()
# np.unique(cam_coor_np[2:3, :].repeat(3, 0).reshape(3, nbr_points))

point_cam_coords, cam_coor # different
# point_coor_v, point_coor_torch, res_list[0]['lidar_point']

cam_coor[:, mask].min(), cam_coor[:, mask].max(), cam_coor[:, mask].shape, mask.shape
# point_cam_coords[:, mask_p].min(), point_cam_coords[:, mask_p].max(), point_cam_coords[:, mask_p].shape, mask.shape

def plot_point_in_camview(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(9, 16))
    fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[0, :], points[1, :], c=coloring, s=dot_size)
    ax.axis('off')
    return fig, ax

view = 'CAM_FRONT'
imgs = res_list[0]['imgs_meta']['img']

img_coor = cam_coor[:2, :].T[mask]
plot_point_in_camview(point_cam_coords[:, mask_p], coloring_p[mask_p], imgs[view], dot_size=1)
# point_cam_coords[:, mask_p], coloring_p[mask_p], im_p

point_coor_v = input_dict['points'].tensor
point_coor_v = point_coor_v.numpy()
point_coor_v = np.concatenate((point_coor_v, np.ones((point_coor_v.shape[0], 1))), axis=1)

point_cam_coords, coloring_p, im_p, mask_p = map_pointcloud_to_image_mask(nusc, point_coor_v, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords[:, mask_p], coloring_p[mask_p], im_p, dot_size=5)

point_cam_coords[:, mask_p]

from nuscenes.nuscenes import NuScenes
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion
import numpy as np

dataroot = '/mnt/data/data/yining/codefield/3dmmpano_240109/data/nuscenes_mini'
nusc = NuScenes(version='v1.0-mini', dataroot=dataroot, verbose=True)

sample_token_v = 'ca9a282c9e77460f8360f564131a8af5'

sample_v = nusc.get('sample', sample_token_v)
pointsensor_token_v = nusc.get('sample_data', sample_v['data']['LIDAR_TOP'])
point_calibrated_sensor_token_v, point_ego_pose_token_v = pointsensor_token_v['calibrated_sensor_token'], pointsensor_token_v['ego_pose_token']
cam_token_v = sample_v['data']['CAM_FRONT']



voxel_coor_torch = torch.load('misc/intermedia_data/voxel_coordinates.pt')
voxel_coor_np = np.load('misc/intermedia_data/voxel_coordinates.npy')
point_coor_torch = torch.load('misc/intermedia_data/point_coordinates.pt')

voxel_idx_coor = voxel_coor_torch.cpu().numpy()
voxel_idx_coor = voxel_idx_coor[:, 1:] # remove batch idx
point_coor = point_coor_torch.cpu().numpy()
# voxel_coor = np.concatenate((voxel_coor[:, 1:], np.ones((voxel_coor.shape[0], 1))), axis=1)
point_coor = np.concatenate((point_coor, np.ones((point_coor.shape[0], 1))), axis=1)

# point_coor_v = input_dict['points'].tensor
# point_coor_v = point_coor_v.numpy()
# point_coor_v = np.concatenate((point_coor, np.ones((point_coor_v.shape[0], 1))), axis=1)

point_coor_v = point_coor
point_cam_coords, coloring_p, im_p, mask_p = map_pointcloud_to_image_mask(nusc, point_coor_v, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


im_p

# img_coor = point_cam_coords[:2, :].T[mask_p]
# coloring_p = coloring_p[mask_p]
# plot_point_in_camview(point_cam_coords[:2, :].T, coloring_p, im_p, dot_size=1)
plot_point_in_camview(point_cam_coords[:, mask_p]*0.5, coloring_p[mask_p]*0.5, im_p*0.5, dot_size=5)

import numpy as np
import torch
from typing import List, Tuple

from nuscenes.nuscenes import NuScenes, NuScenesExplorer
from nuscenes.utils.data_classes import LidarPointCloud
from pyquaternion import Quaternion
from nuscenes.utils.geometry_utils import view_points

import os.path as osp
from PIL import Image
    
def map_pointcloud_to_image_mask_aug(nusc,
                            points_data: np.ndarray,  # (4, N)
                            aug_mat: np.ndarray,
                            point_calibrated_sensor_token: str,
                            point_ego_pose_token: str,
                            camera_token: str,
                            min_dist: float = 1.0,
                            return_img: bool = False
                            ) -> Tuple:
    """
    Given a point cloud and camera sample_data token(view info), map it to the respective image plane.
    :param points_data: Lidar data with shape of (N,4).
    :param camera_token: Camera sample_data token.
    :param min_dist: Distance from the camera below which points are discarded.
    :return (mapped coordinate <np.float: 3, n)>, coloring(optional) <np.float: n>, image(optional) <Image>).
    """
    
    img_H, img_W = 900, 1600 
    cam = nusc.get('sample_data', camera_token) 
    pc = _LidarPointCloud.pack_points(points_data.copy())
        
    # Points live in the point sensor frame. So they need to be transformed via global to the image plane.
    
    # Init step: augment transformation: rotate, scale, translate
    aug_mat_inv = np.linalg.inv(aug_mat)    
    # pc.points = aug_mat_inv.T @ pc.points # R^T @ P^T, (4, 4) @ (4, N)  
    pc.rotate(aug_mat_inv[:3, :3].T)
    
    # First step: transform the pointcloud to the ego vehicle frame for the timestamp of the sweep.
    cs_record = nusc.get('calibrated_sensor', point_calibrated_sensor_token)
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix)
    pc.translate(np.array(cs_record['translation']))
    
    # Second step: transform from ego to the global frame.
    poserecord = nusc.get('ego_pose', point_ego_pose_token)
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix)
    pc.translate(np.array(poserecord['translation']))

    # Third step: transform from global into the ego vehicle frame for the timestamp of the image.
    poserecord = nusc.get('ego_pose', cam['ego_pose_token'])
    pc.translate(-np.array(poserecord['translation']))
    pc.rotate(Quaternion(poserecord['rotation']).rotation_matrix.T)

    # Fourth step: transform from ego into the camera.
    cs_record = nusc.get('calibrated_sensor', cam['calibrated_sensor_token'])
    pc.translate(-np.array(cs_record['translation']))
    pc.rotate(Quaternion(cs_record['rotation']).rotation_matrix.T)
    
    
    depths = pc.points[2, :]
    if return_img:
        im = Image.open(osp.join(nusc.dataroot, cam['filename']))

        # Fifth step: actually take a "picture" of the point cloud.
        # Grab the depths (camera frame z axis points away from the camera).
        # coloring is depth, other visualizations are possible if necessary TODO: show lidar seg/panoptic
        coloring = depths

    # Take the actual picture (matrix multiplication with camera-matrix + renormalization).
    points = view_points(pc.points[:3, :], np.array(cs_record['camera_intrinsic']), normalize=True)
        

    # Remove points that are either outside or behind the camera. Leave a margin of 1 pixel for aesthetic reasons.
    # Also make sure points are at least 1m in front of the camera to avoid seeing the lidar points on the camera
    # casing for non-keyframes which are slightly out of sync.
    mask = np.ones(depths.shape[0], dtype=bool)
    mask = np.logical_and(mask, depths > min_dist)
    mask = np.logical_and(mask, points[0, :] > 1)
    mask = np.logical_and(mask, points[0, :] < img_W - 1)
    mask = np.logical_and(mask, points[1, :] > 1)
    mask = np.logical_and(mask, points[1, :] < img_H - 1)
    # points = points[:, mask]
        
    if return_img:
        # coloring = coloring[mask]
        return points, coloring, im, mask
    else:
        return points, mask
    

# rotation

# R = np.array([[0.0000000, -1.0000000, 0.0000000],
#                 [1.0000000, 0.0000000, 0.0000000],
#                 [0.0000000, 0.0000000, 1.0000000]])

# R2, sin(30), cos(30)
# R = np.array([[0.8660254, -0.5000000, 0.0000000],
#                 [0.5000000, 0.8660254, 0.0000000],
#                 [0.0000000, 0.0000000, 1.0000000]])

# R3, sin(0), cos(0)
R = np.array([[0.0000000, -0.0000000, 1.0000000],
                [0.0000000, 1.0000000, 0.0000000],
                [-1.0000000, 0.0000000, 0.0000000]])


R_pad = np.eye(4)
R_pad[:R.shape[0], :R.shape[1]] = R

point_coor_v_rot = point_coor_v @ R_pad
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(nusc, point_coor_v_rot, R_pad, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)

from numpy import random

resize_lim = (0.95, 1.05)
rot_lim = (-0.78539816, 0.78539816)
trans_lim = 0.2

scale = random.uniform(low=resize_lim[0], high=resize_lim[1])
theta = random.uniform(rot_lim)
translation = np.array([random.normal(0, trans_lim) for i in range(3)])
rotation = np.eye(3)

transform = np.eye(4).astype(np.float32)
transform[:3, :3] = rotation.T * scale
transform[:3, 3] = translation * scale

R_pad = transform
point_coor_v_rot = point_coor_v @ R_pad
point_cam_coords_rot, coloring_p_rot, im_p_rot, mask_p_rot = map_pointcloud_to_image_mask_aug(nusc, point_coor_v_rot, R_pad, point_calibrated_sensor_token_v, point_ego_pose_token_v, cam_token_v, min_dist=1.0, return_img=True)


plot_point_in_camview(point_cam_coords_rot[:, mask_p_rot], coloring_p_rot[mask_p_rot], im_p_rot, dot_size=5)



