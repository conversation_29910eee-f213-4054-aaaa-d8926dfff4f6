_base_ = [
    '../_base_/datasets/semantickitti_panoptic_lpmix.py', '../_base_/models/pointnet2_msg.py',
    '../_base_/schedules/seg-cosine-200e.py', '../_base_/default_runtime.py'
]

train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=40, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# model settings
model = dict(
    data_preprocessor=dict(
        type='_SP3DDataPreprocessor',
        num_samples = 100000,
        # sample_range = ,
        replace = True,
        # return_choices = ,
        voxel=False,
        sample_point=True,
        superpoint=False,
    ),
    backbone=dict(in_channels=4),  
    decode_head=dict(
        num_classes=20,
        ignore_index=19,
        # `class_weight` is generated in data pre-processing, saved in
        # `data/scannet/seg_info/train_label_weight.npy`
        # you can copy paste the values here, or input the file path as
        # `class_weight=data/scannet/seg_info/train_label_weight.npy`
        # loss_decode=dict(class_weight=[
        #     2.389689, 2.7215734, 4.5944676, 4.8543367, 4.096086, 4.907941,
        #     4.690836, 4.512031, 4.623311, 4.9242644, 5.358117, 5.360071,
        #     5.019636, 4.967126, 5.3502126, 5.4023647, 5.4027233, 5.4169416,
        #     5.3954206, 4.6971426
        # ]) #TODO
    ),
    test_cfg=dict(
        num_points=100, #TODO
        block_size=1.5,
        sample_rate=0.5,
        use_normalized_coord=False, #TODO
        batch_size=8)
    )


# dataset settings
# class_names = [
#     'car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist',
#     'motorcyclist', 'road', 'parking', 'sidewalk', 'other-ground', 'building',
#     'fence', 'vegetation', 'trunck', 'terrian', 'pole', 'traffic-sign'
# ]
backend_args = None
train_pipeline = [

    dict(type='LoadPointsFromFile', coord_type='LIDAR', load_dim=4, use_dim=4),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti'),
    dict(type='PointSegClassMapping'),
    dict(
        type='RandomChoice',
        transforms=[
            [
                dict(
                    type='LaserMix',
                    num_areas=[3, 4, 5, 6],
                    pitch_angles=[-25, 3],
                    pre_transform=[
                        dict(
                            type='LoadPointsFromFile',
                            coord_type='LIDAR',
                            load_dim=4,
                            use_dim=4),
                        dict(
                            type='LoadAnnotations3D',
                            with_bbox_3d=False,
                            with_label_3d=False,
                            with_seg_3d=True,
                            seg_3d_dtype='np.int32',
                            seg_offset=2**16,
                            dataset_type='semantickitti'),
                        dict(type='PointSegClassMapping')
                    ],
                    prob=1)
            ],
            [
                dict(
                    type='PolarMix',
                    instance_classes=[0, 1, 2, 3, 4, 5, 6, 7],
                    swap_ratio=0.5,
                    rotate_paste_ratio=1.0,
                    pre_transform=[
                        dict(
                            type='LoadPointsFromFile',
                            coord_type='LIDAR',
                            load_dim=4,
                            use_dim=4),
                        dict(
                            type='LoadAnnotations3D',
                            with_bbox_3d=False,
                            with_label_3d=False,
                            with_seg_3d=True,
                            seg_3d_dtype='np.int32',
                            seg_offset=2**16,
                            dataset_type='semantickitti'),
                        dict(type='PointSegClassMapping')
                    ],
                    prob=1)
            ],
        ],
        prob=[0.5, 0.5]),
    dict(type='PointSample', num_points=-1, sample_range=40.0),
    dict(
        type='RandomFlip3D',
        sync_2d=False,
        flip_ratio_bev_horizontal=0.5,
        flip_ratio_bev_vertical=0.5),
    dict(
        type='GlobalRotScaleTrans',
        rot_range=[-0.78539816, 0.78539816],
        scale_ratio_range=[0.95, 1.05],
        translation_std=[0.1, 0.1, 0.1],
    ),
    dict(type='Pack3DDetInputs', keys=['points', 'pts_semantic_mask'])
]


test_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=4,
        use_dim=4,
        backend_args=backend_args),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    dict(type='Pack3DDetInputs', keys=['points', 'pts_semantic_mask'])
]
# construct a pipeline for data and gt loading in show function
# please keep its loading function consistent with test_pipeline (e.g. client)
eval_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=4,
        use_dim=4,
        backend_args=backend_args),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    dict(type='Pack3DDetInputs', keys=['points', 'pts_semantic_mask'])
]

# train_dataloader = dict(
#     batch_size=2,
#     num_workers=4,
#     sampler=dict(type='DefaultSampler', shuffle=True),
#     dataset=dict(
#         type='RepeatDataset',
#         times=1,
#         dataset=dict(
#             type=dataset_type,
#             data_root=data_root,
#             ann_file='semantickitti_infos_train.pkl',
#             pipeline=train_pipeline,
#             metainfo=metainfo,
#             modality=input_modality,
#             ignore_index=19,
#             backend_args=backend_args)),
# )

# test_dataloader = dict(
#     batch_size=1,
#     num_workers=1,
#     sampler=dict(type='DefaultSampler', shuffle=False),
#     dataset=dict(
#         type='RepeatDataset',
#         times=1,
#         dataset=dict(
#             type=dataset_type,
#             data_root=data_root,
#             ann_file='semantickitti_infos_val.pkl',
#             pipeline=test_pipeline,
#             metainfo=metainfo,
#             modality=input_modality,
#             ignore_index=19,
#             test_mode=True,
#             backend_args=backend_args)),
# )

# val_dataloader = test_dataloader

# val_evaluator = dict(type='SegMetric')
# test_evaluator = val_evaluator

# vis_backends = [dict(type='LocalVisBackend')]
# visualizer = dict(
#     type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')


train_dataloader = dict(batch_size=16, num_workers=2)
val_dataloader = dict(batch_size=16, num_workers=2)
test_dataloader = dict(batch_size=16, num_workers=2)

default_hooks = dict(checkpoint=dict(type='CheckpointHook', interval=5))

custom_imports = dict(
    imports=[
        # 'p3former.backbones.cylinder3d',
        # 'p3former.backbones.pointnet2_sa_msg',
        # 'p3former.backbones.pointnet2_sa_ssg',
        'p3former.data_preprocessors.data_preprocessor_sp',
        'p3former.decode_heads.p3former_head',
        'p3former.segmentors.p3former',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.semantickitti_dataset',
        'datasets.transforms.loading',
        'datasets.transforms.transforms_3d',
    ],
    allow_failed_imports=False)
