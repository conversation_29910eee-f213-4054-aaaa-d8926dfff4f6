_base_ = [
    '../_base_/datasets/semantickitti_spINpreprocess.py', 
    # '../_base_/datasets/semantickitti_panoptic_lpmix.py', 
    '../_base_/models/pointnet2_msg.py',
    # '../_base_/models/pointnet2_msg_p3former.py',
    '../_base_/schedules/seg-cosine-200e.py', '../_base_/default_runtime.py'
]

point_cloud_range=[0, -3.14159265359, -4, 50, 3.14159265359, 2]
dataset_type = 'SP_SemanticKittiDataset'
data_root = 'data/semantickitti/pointclouds'
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=32, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')
spg_prefix='data/semantickitti/superpoint_graph'

# dataset settings
# class_names = [
#     'car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist',
#     'motorcyclist', 'road', 'parking', 'sidewalk', 'other-ground', 'building',
#     'fence', 'vegetation', 'trunck', 'terrian', 'pole', 'traffic-sign'
# ]
backend_args = None
train_pipeline = [
    dict(type='LoadSuperPointsFromFile', 
        use_spg = True,
        spg_config = dict(
            spg_file='', 
            spg_prefix=spg_prefix,
            load_from_file=True),
        coord_type='LIDAR', 
        load_dim=4, 
        use_dim=4),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti'),
    dict(type='PointSegClassMapping'),
    # dict(
    #     type='RandomChoice',
        # transforms=[
            # [
                # dict(
                #     type='LaserMix',
                #     num_areas=[3, 4, 5, 6],
                #     pitch_angles=[-25, 3],
                #     pre_transform=[
                #         dict(
                #             type='LoadPointsFromFile',
                #             coord_type='LIDAR',
                #             load_dim=4,
                #             use_dim=4),
                #         dict(
                #             type='LoadAnnotations3D',
                #             with_bbox_3d=False,
                #             with_label_3d=False,
                #             with_seg_3d=True,
                #             seg_3d_dtype='np.int32',
                #             seg_offset=2**16,
                #             dataset_type='semantickitti'),
                #         dict(type='PointSegClassMapping')
                #     ],
                #     prob=1)
            # ],
            # [
            #     dict(
            #         type='PolarMix',
            #         instance_classes=[0, 1, 2, 3, 4, 5, 6, 7],
            #         swap_ratio=0.5,
            #         rotate_paste_ratio=1.0,
            #         pre_transform=[
            #             dict(
            #                 type='LoadPointsFromFile',
            #                 coord_type='LIDAR',
            #                 load_dim=4,
            #                 use_dim=4),
            #             dict(
            #                 type='LoadAnnotations3D',
            #                 with_bbox_3d=False,
            #                 with_label_3d=False,
            #                 with_seg_3d=True,
            #                 seg_3d_dtype='np.int32',
            #                 seg_offset=2**16,
            #                 dataset_type='semantickitti'),
            #             dict(type='PointSegClassMapping')
            #         ],
            #         prob=1)
            # ],
        # ],
        # prob=[0.5, 0.5]),
    # dict(type='PointSample', num_points=-1, sample_range=40.0),
    # dict(
    #     type='RandomFlip3D',
    #     sync_2d=False,
    #     flip_ratio_bev_horizontal=0.5,
    #     flip_ratio_bev_vertical=0.5),
    # dict(
    #     type='GlobalRotScaleTrans',
    #     rot_range=[-0.78539816, 0.78539816],
    #     scale_ratio_range=[0.95, 1.05],
    #     translation_std=[0.1, 0.1, 0.1],
    # ),
    dict(type='Pack3DSPInputs', keys=['points', 'pts_semantic_mask'])
]


test_pipeline = [
    dict(type='LoadSuperPointsFromFile', 
        use_spg = True,
        spg_config = dict(
            spg_file='', 
            spg_prefix=spg_prefix,
            load_from_file=True),
        coord_type='LIDAR', 
        load_dim=4, 
        use_dim=4),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    dict(type='Pack3DSPInputs', keys=['points', 'pts_semantic_mask'])
]
# construct a pipeline for data and gt loading in show function
# please keep its loading function consistent with test_pipeline (e.g. client)
eval_pipeline = [
    dict(type='LoadSuperPointsFromFile', 
        use_spg = True,
        spg_config = dict(
            spg_file='', 
            spg_prefix=spg_prefix,
            load_from_file=True),
        coord_type='LIDAR', 
        load_dim=4, 
        use_dim=4),
    dict(
        type='LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_seg_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    dict(type='Pack3DSPInputs', keys=['points', 'pts_semantic_mask', 'pts_instance_mask'])
]

# val_evaluator = dict(type='SegMetric')
val_evaluator = dict(
    type='_PointPanopticSegMetric',
    thing_class_inds=[0, 1, 2, 3, 4, 5, 6, 7],
    stuff_class_inds=[8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
    min_num_points=50,
    id_offset=65536,
    dataset_type='semantickitti',
    learning_map_inv=dict({
        0: 10,
        1: 11,
        2: 15,
        3: 18,
        4: 20,
        5: 30,
        6: 31,
        7: 32,
        8: 40,
        9: 44,
        10: 48,
        11: 49,
        12: 50,
        13: 51,
        14: 70,
        15: 71,
        16: 72,
        17: 80,
        18: 81,
        19: 0
    }))
test_evaluator = val_evaluator

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')

train_dataloader = dict(batch_size=8, num_workers=4)
val_dataloader = dict(batch_size=8, num_workers=4)
test_dataloader = dict(batch_size=8, num_workers=4)

default_hooks = dict(checkpoint=dict(type='CheckpointHook', interval=5))

custom_imports = dict(
    imports=[
        # 'p3former.backbones.cylinder3d',
        # 'p3former.backbones.pointnet2_sa_msg', #TODO-YINING: model already registered.
        # 'p3former.backbones.pointnet2_sa_ssg',
        'p3former.data_preprocessors.data_preprocessor_sp',
        # 'p3former.data_preprocessors.data_preprocessor',
        'p3former.decode_heads.p3former_head',
        'p3former.segmentors.p3former',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.semantickitti_dataset',
        'datasets.transforms.loading',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
    ],
    allow_failed_imports=False)

# model settings
model = dict(
    type='_PointP3Former',
    data_preprocessor=dict(
        type='_SP3DDataPreprocessor',
        num_samples = 100000,
        # sample_range = ,
        replace = True,
        # return_choices = ,
        voxel=False,
        sample_point=True,
        superpoint=True,
        load_from_file=True,
        spg_prefix=spg_prefix,
    ),
    # backbone=dict(in_channels=4), 
    backbone=dict(
        type='PointNet2SAMSG',
        in_channels=4,
        num_points=(4096, 1024, 256, 64),
        # radii=((0.5, 1.0), (1.0, 2.0), (2.0, 4.0), (4.0, 8.0)), #TODO-YINING: not sure which is better
        radii=((0.1, 0.5), (0.5, 1.0), (1.0, 2.0), (2.0, 4.0)),
        num_samples=((16, 32), (16, 32), (16, 32), (16, 32)),
        sa_channels=(((16, 16, 32), (32, 32, 64)), ((64, 64, 128), (64, 96,
                                                                    128)),
                     ((128, 196, 256), (128, 196, 256)), ((256, 256, 512),
                                                          (256, 384, 512))),
        fps_mods=(('D-FPS'), ('D-FPS'), ('D-FPS'), ('D-FPS')),
        fps_sample_range_lists=((-1), (-1), (-1), (-1)),
        aggregation_channels=(None, None, None, None),
        dilated_group=(False, False, False, False),
        out_indices=(0, 1, 2, 3),
        norm_cfg=dict(type='BN2d', eps=1e-3, momentum=0.1),
        sa_cfg=dict(
            type='PointSAModuleMSG',
            pool_mod='max',
            use_xyz=True,
            normalize_xyz=False)),
    neck=dict(
        type='PointNetFPNeck',
        fp_channels=((1536, 512, 512), (768, 512, 512), (608, 256, 256),
                     (257, 128, 128))), 
    decode_head=dict(
        type='_PointP3FormerHead',
        point_wise_loss=True,
        sp_fusion=True,
        aggre_mode='avg',
        num_classes=20,
        ignore_index=19,
        num_queries=128,
        embed_dims=128,
        point_cloud_range=[0, -3.14159265359, -4, 50, 3.14159265359, 2],
        assigner_zero_layer_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        assigner_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', gamma=4.0,alpha=0.25,weight=1.0),
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        sampler_cfg=dict(type='_MaskPseudoSampler'),
        loss_mask=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            reduction='mean',
            loss_weight=1.0),
        loss_dice=dict(type='mmdet.DiceLoss', loss_weight=2.0),
        loss_cls=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=4.0,
            alpha=0.25,
            loss_weight=1.0),
        # `class_weight` is generated in data pre-processing, saved in
        # `data/scannet/seg_info/train_label_weight.npy`
        # you can copy paste the values here, or input the file path as
        # `class_weight=data/scannet/seg_info/train_label_weight.npy`
        # loss_decode=dict(class_weight=[
        #     2.389689, 2.7215734, 4.5944676, 4.8543367, 4.096086, 4.907941,
        #     4.690836, 4.512031, 4.623311, 4.9242644, 5.358117, 5.360071,
        #     5.019636, 4.967126, 5.3502126, 5.4023647, 5.4027233, 5.4169416,
        #     5.3954206, 4.6971426
        # ]) #TODO
        thing_class=[0,1,2,3,4,5,6,7], #TODO-YINING: P3Former param
        stuff_class=[8,9,10,11,12,13,14,15,16,17,18],
    ),
    train_cfg=None,
    test_cfg=dict(
        mode='whole',
        num_points=100, #TODO
        block_size=1.5,
        sample_rate=0.5,
        use_normalized_coord=False, #TODO
        batch_size=1)
    )
