_base_ = './pointnet2_ssg.py'

# train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=40, val_interval=1)
# val_cfg = dict(type='ValLoop')
# test_cfg = dict(type='TestLoop')

# model settings
model = dict(
    data_preprocessor=dict(
        type='Det3DDataPreprocessor',
    ),
    backbone=dict(
        _delete_=True,
        type='PointNet2SAMSG',
        in_channels=4,  # [x,y,z,intensity] for semantickitti
        num_points=(16384, 4096, 1024, 256),  # refer to ASAP-Net-PointNet2
        radii=((0.5, 1.0), (1.0, 2.0), (2.0, 4.0), (4.0, 8.0)),
        # num_points=(4096, 1024, 256, 64),       # refer to mmdet3d/point-rcnn
        # radii=((0.1, 0.5), (0.5, 1.0), (1.0, 2.0), (2.0, 4.0)), 
        num_samples=((16, 32), (16, 32), (16, 32), (16, 32)),
        sa_channels=(((16, 16, 32), (32, 32, 64)), ((64, 64, 128), (64, 96,
                                                                    128)),
                     ((128, 196, 256), (128, 196, 256)), ((256, 256, 512),
                                                          (256, 384, 512))),
        aggregation_channels=(None, None, None, None),
        fps_mods=(('D-FPS'), ('D-FPS'), ('D-FPS'), ('D-FPS')),
        fps_sample_range_lists=((-1), (-1), (-1), (-1)),
        dilated_group=(False, False, False, False),
        out_indices=(0, 1, 2, 3),
        sa_cfg=dict(
            type='PointSAModuleMSG',
            pool_mod='max',
            use_xyz=True,
            normalize_xyz=False)),
    decode_head=dict(
        type='_P3FormerHead',
        # num_classes=20,
        # num_queries=128,
        # embed_dims=128,
        point_cloud_range=[0, -3.14159265359, -4, 50, 3.14159265359, 2],
        assigner_zero_layer_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        assigner_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', gamma=4.0,alpha=0.25,weight=1.0),
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        sampler_cfg=dict(type='_MaskPseudoSampler'),
        loss_mask=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            reduction='mean',
            loss_weight=1.0),
        loss_dice=dict(type='mmdet.DiceLoss', loss_weight=2.0),
        loss_cls=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=4.0,
            alpha=0.25,
            loss_weight=1.0),
    ),
    train_cfg=None,
    test_cfg=dict(mode='whole'),
)
