# For SemanticKitti we usually do 19-class segmentation.
# For labels_map we follow the uniform format of MMDetection & MMSegmentation
# i.e. we consider the unlabeled class as the last one, which is different
# from the original implementation of some methods e.g. Cylinder3D.
dataset_type = 'SP_SemanticKittiDataset'
use_spg = False
spg_prefix='data/semantickitti/superpoint_graph'
data_root = 'data/semantickitti/pointclouds'
spg_config = dict(
            spg_file='', 
            spg_prefix=spg_prefix,
            load_from_file=True)
class_names = [
    'car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist',
    'motorcyclist', 'road', 'parking', 'sidewalk', 'other-ground', 'building',
    'fence', 'vegetation', 'trunck', 'terrian', 'pole', 'traffic-sign'
]
labels_map = {
    0: 19,  # "unlabeled"
    1: 19,  # "outlier" mapped to "unlabeled" --------------mapped
    10: 0,  # "car"
    11: 1,  # "bicycle"
    13: 4,  # "bus" mapped to "other-vehicle" --------------mapped
    15: 2,  # "motorcycle"
    16: 4,  # "on-rails" mapped to "other-vehicle" ---------mapped
    18: 3,  # "truck"
    20: 4,  # "other-vehicle"
    30: 5,  # "person"
    31: 6,  # "bicyclist"
    32: 7,  # "motorcyclist"
    40: 8,  # "road"
    44: 9,  # "parking"
    48: 10,  # "sidewalk"
    49: 11,  # "other-ground"
    50: 12,  # "building"
    51: 13,  # "fence"
    52: 19,  # "other-structure" mapped to "unlabeled" ------mapped
    60: 8,  # "lane-marking" to "road" ---------------------mapped
    70: 14,  # "vegetation"
    71: 15,  # "trunk"
    72: 16,  # "terrain"
    80: 17,  # "pole"
    81: 18,  # "traffic-sign"
    99: 19,  # "other-object" to "unlabeled" ----------------mapped
    252: 0,  # "moving-car" to "car" ------------------------mapped
    253: 6,  # "moving-bicyclist" to "bicyclist" ------------mapped
    254: 5,  # "moving-person" to "person" ------------------mapped
    255: 7,  # "moving-motorcyclist" to "motorcyclist" ------mapped
    256: 4,  # "moving-on-rails" mapped to "other-vehic------mapped
    257: 4,  # "moving-bus" mapped to "other-vehicle" -------mapped
    258: 3,  # "moving-truck" to "truck" --------------------mapped
    259: 4  # "moving-other"-vehicle to "other-vehicle"-----mapped
}

learning_map_inv = { # inverse of previous map
  0: 10,      # "unlabeled", and others ignored
  1: 11,     # "car"
  2: 15,     # "bicycle"
  3: 18,     # "motorcycle"
  4: 20,     # "truck"
  5: 30,     # "other-vehicle"
  6: 31,     # "person"
  7: 32,     # "bicyclist"
  8: 40,     # "motorcyclist"
  9: 44,     # "road"
  10: 48,    # "parking"
  11: 49,    # "sidewalk"
  12: 50,    # "other-ground"
  13: 51,    # "building"
  14: 70,    # "fence"
  15: 71,    # "vegetation"
  16: 72,    # "trunk"
  17: 80,    # "terrain"
  18: 81,    # "pole"
  19: 0    # "traffic-sign"
}

metainfo = dict(
    classes=class_names, seg_label_mapping=labels_map, max_label=259)

input_modality = dict(use_lidar=True, use_camera=False)

# Example to use different file client
# Method 1: simply set the data root and let the file I/O module
# automatically infer from prefix (not support LMDB and Memcache yet)

# data_root = 's3://openmmlab/datasets/detection3d/semantickitti/'

# Method 2: Use backend_args, file_client_args in versions before 1.1.0rc4
# backend_args = dict(
#     backend='petrel',
#     path_mapping=dict({
#         './data/': 's3://openmmlab/datasets/detection3d/',
#          'data/': 's3://openmmlab/datasets/detection3d/'
#      }))
backend_args = None

# pre_transform = [
#     dict(type='LoadSuperPointsFromFile', 
#         use_spg = True,
#         spg_config = dict(
#             spg_file='', 
#             spg_prefix=spg_prefix,
#             load_from_file=True),
#         coord_type='LIDAR', 
#         load_dim=4, 
#         use_dim=4),
#     dict(
#         type='_LoadAnnotations3D',
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.int32',
#         seg_offset=2**16,
#         dataset_type='semantickitti',
#         backend_args=backend_args),
#     dict(type='PointSegClassMapping', )]

train_pipeline = [
    dict(type='LoadSuperPointsFromFile', 
        use_spg = True,
        spg_config = dict(
            spg_file='', 
            spg_prefix=spg_prefix,
            load_from_file=True),
        coord_type='LIDAR', 
        load_dim=4, 
        use_dim=4),
    dict(
        type='_LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    # dict(
        # type='RandomChoice',
        # transforms=[
            # [
            #     dict(
            #         type='_LaserMix',
            #         num_areas=[3, 4, 5, 6],
            #         pitch_angles=[-25, 3],
            #         pre_transform=[
            #             dict(
            #                 type='LoadPointsFromFile',
            #                 coord_type='LIDAR',
            #                 load_dim=4,
            #                 use_dim=4),
            #             dict(
            #                 type='_LoadAnnotations3D',
            #                 with_bbox_3d=False,
            #                 with_label_3d=False,
            #                 with_panoptic_3d=True,
            #                 seg_3d_dtype='np.int32',
            #                 seg_offset=2**16,
            #                 dataset_type='semantickitti'),
            #             dict(type='PointSegClassMapping')
            #         ],
            #         prob=0.5)
            # ],
            # [
        #         dict(
        #             type='_PolarMix',
        #             instance_classes=[0, 1, 2, 3, 4, 5, 6, 7],
        #             swap_ratio=0.5,
        #             rotate_paste_ratio=1.0,
        #             pre_transform=[
        #                 dict(
        #                     type='LoadPointsFromFile',
        #                     coord_type='LIDAR',
        #                     load_dim=4,
        #                     use_dim=4),
        #                 dict(
        #                     type='_LoadAnnotations3D',
        #                     with_bbox_3d=False,
        #                     with_label_3d=False,
        #                     with_panoptic_3d=True,  
        #                     seg_3d_dtype='np.int32',
        #                     seg_offset=2**16,
        #                     dataset_type='semantickitti'),
        #                 dict(type='PointSegClassMapping')
        #             ],
        #             prob=1)
        #     # ],
        # ],
        # prob=[0.2, 0.8]),
    dict(
        type='RandomFlip3D',
        sync_2d=False,
        flip_ratio_bev_horizontal=0.5,
        flip_ratio_bev_vertical=0.5),
    dict(
        type='GlobalRotScaleTrans',
        rot_range=[-0.78539816, 0.78539816],
        scale_ratio_range=[0.95, 1.05],
        translation_std=[0.1, 0.1, 0.1],
    ),
    dict(type='Pack3DSPInputs', keys=['points', 'pts_semantic_mask', 'pts_instance_mask'])
]

test_pipeline = [
    dict(type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=4,
        use_dim=4,
        backend_args=backend_args),
    dict(
        type='_LoadAnnotations3D',
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.int32',
        seg_offset=2**16,
        dataset_type='semantickitti',
        backend_args=backend_args),
    dict(type='PointSegClassMapping', ),
    dict(type='_Pack3DDetInputs', keys=['points', 'pts_semantic_mask', 'pts_instance_mask'])
]


train_dataloader = dict(
    batch_size=4,
    num_workers=4,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type='RepeatDataset',
        times=1,
        dataset=dict(
            type=dataset_type,     
            data_root=data_root,
            data_prefix = dict(
                     pts='',
                     img='',
                     pts_instance_mask='',
                     pts_semantic_mask='',
                     pts_panoptic_mask='',),
            ann_file='semantickitti_infos_train_4.pkl',
            pipeline=train_pipeline,
            metainfo=metainfo,
            modality=input_modality,
            ignore_index=19,
            use_spg=use_spg,
            spg_config=spg_config,
            backend_args=backend_args)),
)

test_dataloader = dict(
    batch_size=1,
    num_workers=1,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type='RepeatDataset',
        times=1,
        dataset=dict(
            type=dataset_type,
            data_root=data_root,
            data_prefix = dict(
                     pts='',
                     img='',
                     pts_instance_mask='',
                     pts_semantic_mask='',
                     pts_panoptic_mask='',),
            ann_file='semantickitti_infos_val.pkl',
            pipeline=test_pipeline,
            metainfo=metainfo,
            modality=input_modality,
            ignore_index=19,
            test_mode=True,
            backend_args=backend_args)),
)

val_dataloader = test_dataloader

val_evaluator = dict(type='_PanopticSegMetric',
                    thing_class_inds=[0,1,2,3,4,5,6,7],
                    stuff_class_inds=[8,9,10,11,12,13,14,15,16,17,18],
                    min_num_points=50,
                    id_offset = 2**16,
                    dataset_type='semantickitti',
                    learning_map_inv=learning_map_inv)
test_evaluator = val_evaluator

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')
