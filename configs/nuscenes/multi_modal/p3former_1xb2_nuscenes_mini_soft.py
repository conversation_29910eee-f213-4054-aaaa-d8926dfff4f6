_base_ = [
    '../../_base_/default_runtime.py'
]

##########################################################
# DATA PATHS: <PERSON>ANGE THESE VALUES TO YOUR FOLDERS
proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
dataset_type = 'NuScenesSegDataset'
data_root = proj_path+'data/nuscenes_mini/'
data_prefix = dict(
    pts='samples/LIDAR_TOP',
    pts_semantic_mask='',
    # pts_semantic_mask='lidarseg/v1.0-trainval',
    pts_panoptic_mask='',
    # pts_panoptic_mask='lidarseg/v1.0-trainval',
    CAM_FRONT='samples/CAM_FRONT',
    CAM_FRONT_LEFT='samples/CAM_FRONT_LEFT',
    CAM_FRONT_RIGHT='samples/CAM_FRONT_RIGHT',
    CAM_BACK='samples/CAM_BACK',
    CAM_BACK_RIGHT='samples/CAM_BACK_RIGHT',
    CAM_BACK_LEFT='samples/CAM_BACK_LEFT')
# spg_prefix=proj_path+'data/semantickitti/superpoint_graph'
mode='train' # train or test, used to determine sample_point
##########################################################

##########################################################
# SETTINGS FUSION MODE
fusion_mode='centeroid'
p_fusion_mode='max'
##########################################################


##########################################################
# SETTINGS FOR INPUT: POINTS AND IMAGES
if dataset_type=='NuScenesSegDataset':
    point_cloud_range = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
    point_cloud_range_polar = [0, -3.14159265359, -5., 54.0, 3.14159265359, 3.]

    thing_class = [1,2,3,4,5,6,7,8,9,10]
    stuff_class = [11,12,13,14,15,16]
    ignore_index = 0
    num_classes = 17
    
    seg_offset = 1000
else:
    raise NotImplementedError()
# Superpoint related variables
# use_spg=False
# load_from_file=use_spg
# spg_config = dict(
#             spg_file='', 
#             spg_prefix=spg_prefix,
#             load_from_file=load_from_file)

# Voxelization
grid_shape = [480, 360, 32] # <-- 

# Sample point
# sample_point = False
# sample_point_train=True
# sample_point_test=False  # During test
# if mode == 'train':
#     sample_point = sample_point_train
# elif mode == 'test':
#     sample_point = sample_point_test
# voxel=False

# Loss: When using superpoint, either use point_wise_loss or superpoint_wise_loss, point_wise_loss gives better result
# point_wise_loss=True

# Forward + Predict. If use superpoint, this should be true to use p2sp and sp2p during forward and predict
# sp_fusion=use_spg

# Visualization
if mode == 'test':
    save_semantic=True
else:
    save_semantic=False

backend_args = None  # storage backend
##########################################################


##########################################################
# DATASET CONFIG: nuScenes 
if dataset_type == 'NuScenesSegDataset':
    class_names = [
        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
    ]
else:
    raise NotImplementedError()

metainfo = dict(classes=class_names)

input_modality = dict( # <-- not used
    use_lidar=True,
    use_camera=True,
    use_radar=False,
    use_map=False,
    use_external=False)


# TODO: Check this
# pre_transform = [
#     dict(
#         type='LoadPointsFromFile',
#         coord_type='LIDAR',
#         load_dim=4,
#         use_dim=4,
#         backend_args=backend_args),
#     dict(
#         type='_LoadAnnotations3D',
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.int32',
#         seg_offset=2**16,
#         dataset_type='semantickitti',
#         backend_args=backend_args),
#     dict(type='PointSegClassMapping', )]

##########################################################
# FileNotFoundError: [Errno 2] No such file or directory: 'panoptic/v1.0-mini/9d9bf11fb0e144c8b446d54a8a00184f_panoptic.npz'

##########################################################
# DATA LOADING PIPELINES
train_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=3,
        backend_args=backend_args),
    # dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    # dict(type='PointSample', num_points=-1, sample_range=40.0),
    # dict(
    #     type='GlobalRotScaleTrans',
    #     rot_range=[-0.3925, 0.3925],
    #     scale_ratio_range=[0.95, 1.05],
    #     translation_std=[0, 0, 0]),
    # dict(type='RandomFlip3D', flip_ratio_bev_horizontal=0.5),
    # dict(type='PointsRangeFilter', point_cloud_range=point_cloud_range),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img']
        )
]

test_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=3,
        backend_args=backend_args),
    # dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img']
        )
]

# construct a pipeline for data and gt loading in show function
# please keep its loading function consistent with test_pipeline (e.g. client)
eval_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=3,
        backend_args=backend_args),
    dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img']
        )
]
##########################################################


##########################################################
# DATALOADER: Remember to check batch size and num_worker

train_dataloader = dict(
    batch_size=6,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        ann_file='nuscenes_infos_train.pkl',
        pipeline=train_pipeline,
        # metainfo=metainfo,
        # modality=input_modality,
        test_mode=False),
)


test_dataloader = dict(
    batch_size=6,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        ann_file='nuscenes_infos_val.pkl',
        pipeline=test_pipeline,
        # modality=input_modality,
        test_mode=True)
)

val_dataloader = test_dataloader
##########################################################


##########################################################
# TRAIN, VALIDATION, TEST LOOPS
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=240, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')
##########################################################


##########################################################
# MODEL
model = dict(
    type='_SOFT_P3Former_PHYMASK',
    use_physical_points=True,
    p_fusion_mode=p_fusion_mode,
    img_backbone=dict(
        type='mmdet.ResNet', # detail params should be further discussed
        depth=50,
        num_stages=4,
        out_indices=(1,2,3), # tpvformer use 1,2,3
        frozen_stages=1,
        norm_cfg=dict(type='BN2d', requires_grad=False),
        norm_eval=True,
        style='caffe',
        dcn=dict(
            type='DCNv2', deform_groups=1, fallback_on_stride=False
        ),  # original DCNv2 will print log when perform load_state_dict
        stage_with_dcn=(False, False, True, True),
        init_cfg=dict(
            type='Pretrained',
            checkpoint='open-mmlab://detectron2/resnet50_caffe')), # /home/<USER>/.cache/torch/hub/checkpoints/resnet101_msra-6cc46731.pth

    data_preprocessor=dict(
        type='_Seg3DDataPreprocessor',
        voxel=True,
        voxel_type='cylindrical',
        return_points=True,
        voxel_layer=dict(
            grid_shape=grid_shape,
            point_cloud_range=point_cloud_range_polar,
            max_num_points=-1,
            max_voxels=-1,
        ),
    ),
    voxel_encoder=dict(
        type='SegVFE',
        feat_channels=[64, 128, 256, 256],
        in_channels=5,
        with_voxel_center=True,
        feat_compression=16,
        return_point_feats=False),
    backbone=dict(
        type='_Asymm3DSpconv',
        grid_size=grid_shape,
        input_channels=16,
        base_channels=32,
        more_conv=True,
        out_channels=256,
        norm_cfg=dict(type='BN1d', eps=1e-5, momentum=0.1)),
    decode_head=dict(
        type='_SOFT_P3FormerHead', # _MMP3FormerHead
        num_decoder_layers=6,
        num_queries=128,
        embed_dims=256,
        cls_channels=(256, 256, num_classes), # 17 is the number of classes
        mask_channels=(256, 256, 256, 256, 256),
        thing_class=thing_class,
        stuff_class=stuff_class,
        ignore_index=ignore_index,
        num_classes=num_classes,
        point_cloud_range=point_cloud_range_polar,
        point_cloud_range_cart=point_cloud_range,
        assigner_zero_layer_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        assigner_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', gamma=4.0,alpha=0.25,weight=1.0),
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        sampler_cfg=dict(type='_MaskPseudoSampler'),
        loss_mask=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=2.0,
            alpha=0.25,
            reduction='mean',
            loss_weight=1.0),
        loss_dice=dict(type='mmdet.DiceLoss', loss_weight=2.0),
        loss_cls=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=4.0,
            alpha=0.25,
            loss_weight=1.0),
    ),
    
    train_cfg=None,
    test_cfg=dict(mode='whole'),
#     test_cfg=dict(
#         mode='whole',
#         num_points=100, #TODO
#         block_size=1.5,
#         sample_rate=0.5,
#         use_normalized_coord=False, #TODO
#         batch_size=1)
)

lr = 0.0008
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='AdamW', lr=lr, weight_decay=0.01))

##########################################################


##########################################################
# EVALUATION
val_evaluator = dict(
    type='_PanopticSegMetric',
    thing_class_inds=thing_class, 
    stuff_class_inds=stuff_class,
    min_num_points=15,
    id_offset=seg_offset,
    dataset_type='nuscenes',
    learning_map_inv=dict({ #TODO-YINING: only for semantickitti
        0: 10,
        1: 11,
        2: 15,
        3: 18,
        4: 20,
        5: 30,
        6: 31,
        7: 32,
        8: 40,
        9: 44,
        10: 48,
        11: 49,
        12: 50,
        13: 51,
        14: 70,
        15: 71,
        16: 72,
        17: 80,
        18: 81,
        19: 0
    }))
test_evaluator = val_evaluator
##########################################################


##########################################################
# VISUALIZATION BACKEND
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')
##########################################################


##########################################################
# HOOKS
default_hooks = dict(checkpoint=dict(type='CheckpointHook', interval=5))
custom_hooks = [
    dict(type='SavePredictionHook', save_semantic=save_semantic)
]
##########################################################


##########################################################
# CUSTOM IMPORTS #TODO: Clean up
custom_imports = dict(
    imports=[
        'p3former.backbones.cylinder3d',
        'p3former.data_preprocessors.data_preprocessor_sp',
        'p3former.decode_heads.p3former_head',
        'p3former.decode_heads.soft_p3former_head',
        'p3former.segmentors.p3former',
        'p3former.segmentors.p3former_mm_soft',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.nuscenes_dataset_seg',
        'datasets.transforms.loading',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
        'hooks.prediction_hook',
    ],
    allow_failed_imports=False)