_base_ = [
    '../../_base_/default_runtime.py'
]

##########################################################
# DATA PATHS: CHANGE THESE VALUES TO YOUR FOLDERS
device='HPC1'
if device == 'HPC5':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'HPC3':
    proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
elif device == 'HPC1':
    proj_path='/mnt/data/data/yining/codefield/3dmmpano_240109/'
else:
    proj_path = '.'
    
task_mode='debug' # 'train' or 'debug'
# dataset settings
dataset_type = 'NuScenesSegDataset' # or '_CocoPanopticDataset'
data_root = proj_path+'data/nuscenes_full/' if task_mode=='train' else proj_path+'data/nuscenes_mini/'
data_prefix = dict(
    pts='samples/LIDAR_TOP',
    pts_semantic_mask='',
    # pts_semantic_mask='lidarseg/v1.0-trainval',
    pts_panoptic_mask='',
    # pts_panoptic_mask='lidarseg/v1.0-trainval',
    CAM_FRONT='samples/CAM_FRONT',
    CAM_FRONT_LEFT='samples/CAM_FRONT_LEFT',
    CAM_FRONT_RIGHT='samples/CAM_FRONT_RIGHT',
    CAM_BACK='samples/CAM_BACK',
    CAM_BACK_RIGHT='samples/CAM_BACK_RIGHT',
    CAM_BACK_LEFT='samples/CAM_BACK_LEFT')
mode='debug' # train or test, used to determine save validation results or not
################################################################

##########################################################
# image related settings
img_scale = (640, 360) # *0.4
img_norm_cfg = dict(mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)
# img_norm_cfg = dict(
#     mean=[103.530, 116.280, 123.675], std=[57.375, 57.120, 58.395], to_rgb=False)
resnet18_path = 'checkpoints/resnet18-5c106cde.pth'
feature_map_dims = [128] # for resnet18: [512, 1024, 2048]
##########################################################

##########################################################
# SETTINGS FOR POINTS  
use_intensity = False
vfe_inchannel = 6 if use_intensity else 5
lidar_dim = 4 if use_intensity else 3

if dataset_type=='NuScenesSegDataset':
    point_cloud_range = [-50.0, -50.0, -5.0, 50.0, 50.0, 3.0]
    point_cloud_range_polar = [0, -3.14159265359, -5., 50.0, 3.14159265359, 3.]

    thing_class = [1,2,3,4,5,6,7,8,9,10]
    stuff_class = [11,12,13,14,15,16]
    ignore_index = 0
    num_classes = 17
    
    seg_offset = 1000
else:
    raise NotImplementedError()


# Voxelization
grid_shape = [480, 360, 32] # <-- 


# Visualization
if mode == 'test':
    save_semantic=True
else:
    save_semantic=False

backend_args = None  # storage backend
##########################################################


##########################################################
# DATASET CONFIG: nuScenes 
if dataset_type == 'NuScenesSegDataset':
    class_names = [
        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
    ]
else:
    raise NotImplementedError()

metainfo = dict(classes=class_names)

input_modality = dict( # <-- not used
    use_lidar=True,
    use_camera=True,
    use_radar=False,
    use_map=False,
    use_external=False)
##########################################################


##########################################################
# DATA LOADING PIPELINES
fusion_mode='centeroid'

train_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    # dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=False,
        fusion_mode=fusion_mode, #TODO-YINING: what is this?
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    # dict(type='_ImageyNormalize', **img_norm_cfg), # do it in pre-processing
    # dict(type='_ImagePad', size_divisor=32),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img', 
                   'pcd_rotation', 'pcd_scale_factor', 'pcd_trans', 'lidar_aug_matrix', 'img_aug_matrix', 'scale_factor']
        )
]

test_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    # dict(type='_ImageyNormalize', **img_norm_cfg), # do it in pre-processing
    # dict(type='_ImagePad', size_divisor=32),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img', 
                   'pcd_rotation', 'pcd_scale_factor', 'pcd_trans', 'lidar_aug_matrix', 'img_aug_matrix', 'scale_factor']
        )
]

# construct a pipeline for data and gt loading in show function
# please keep its loading function consistent with test_pipeline (e.g. client)
eval_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    # dict(type='_ImageyNormalize', **img_norm_cfg), # do it in pre-processing
    # dict(type='_ImagePad', size_divisor=32),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img']
        )
]

#TODO-YINING: add image augmentation
# train_pipeline = [
#     dict(type='LoadImageFromFile'),
#     dict(type='LoadAnnotations'),
#     dict(
#         type='RandomResize',
#         scale=(2048, 1024),
#         ratio_range=(0.5, 2.0),
#         keep_ratio=True),
#     dict(type='RandomCrop', crop_size=crop_size, cat_max_ratio=0.75),
#     dict(type='RandomFlip', prob=0.5),
#     dict(type='PhotoMetricDistortion'),
#     dict(type='PackSegInputs')
# ]
# test_pipeline = [
#     dict(type='LoadImageFromFile'),
#     dict(type='Resize', scale=(2048, 1024), keep_ratio=True),
#     # add loading annotation after ``Resize`` because ground truth
#     # does not need to do resize data transform
#     dict(type='LoadAnnotations'),
#     dict(type='PackSegInputs')
# ]
# img_ratios = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75]
# tta_pipeline = [
#     dict(type='LoadImageFromFile', backend_args=None),
#     dict(
#         type='TestTimeAug',
#         transforms=[
#             [
#                 dict(type='Resize', scale_factor=r, keep_ratio=True)
#                 for r in img_ratios
#             ],
#             [
#                 dict(type='RandomFlip', prob=0., direction='horizontal'),
#                 dict(type='RandomFlip', prob=1., direction='horizontal')
#             ], [dict(type='LoadAnnotations')], [dict(type='PackSegInputs')]
#         ])
# ]

##########################################################


##########################################################
# DATALOADER: Remember to check batch size and num_worker

train_dataloader = dict(
    batch_size=3,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        ann_file='nuscenes_infos_val.pkl',
        pipeline=train_pipeline,
        test_mode=False))
test_dataloader = dict(
    batch_size=3,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        ann_file='nuscenes_infos_val.pkl',
        pipeline=test_pipeline,
        # modality=input_modality,
        test_mode=True)
)

val_dataloader = test_dataloader
##########################################################

##########################################################
# TRAIN, VALIDATION, TEST LOOPS
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=120, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')
##########################################################



##########################################################
# MODEL

# data_preprocessor = dict(
#     type='SegDataPreProcessor',
#     mean=[123.675, 116.28, 103.53],
#     std=[58.395, 57.12, 57.375],
#     bgr_to_rgb=True,
#     pad_val=0,
#     seg_pad_val=255,
#     size=crop_size,
#     test_cfg=dict(size_divisor=32))
# num_classes = 19


m2f_pano_head=dict(
        type='POI_Mask2FormerHead',
        in_channels=[256, 512, 1024, 2048],  # pass to pixel_decoder inside
        strides=[4, 8, 16, 32],
        feat_channels=256,
        out_channels=256,
        thing_class=thing_class,
        stuff_class=stuff_class,
        ignore_index=ignore_index,
        num_things_classes=len(thing_class),
        num_stuff_classes=len(stuff_class),
        num_classes=num_classes,
        num_queries=100,
        use_sem_loss=True,
        num_transformer_feat_level=3,
        pixel_decoder=dict(
            type='mmdet.TransformerEncoderPixelDecoder',
            norm_cfg=dict(type='GN', num_groups=32),
            act_cfg=dict(type='ReLU'),
            encoder=dict(  # DetrTransformerEncoder
                num_layers=6,
                layer_cfg=dict(  # DetrTransformerEncoderLayer
                    self_attn_cfg=dict(  # MultiheadAttention
                        embed_dims=256,
                        num_heads=8,
                        dropout=0.1,
                        batch_first=True),
                    ffn_cfg=dict(
                        embed_dims=256,
                        feedforward_channels=2048,
                        num_fcs=2,
                        ffn_drop=0.1,
                        act_cfg=dict(type='ReLU', inplace=True)))),
            positional_encoding=dict(num_feats=128, normalize=True)),
        # V2
        # pixel_decoder=dict(
        #     type='mmdet.PixelDecoder',
        #     norm_cfg=dict(type='GN', num_groups=32),
        #     act_cfg=dict(type='ReLU')),
        # V1
        # pixel_decoder=dict(
        #     type='mmdet.MSDeformAttnPixelDecoder',
        #     num_outs=3, # change 3 to 1 for debug L
        #     norm_cfg=dict(type='GN', num_groups=32),
        #     act_cfg=dict(type='ReLU'),
        #     encoder=dict(  # DeformableDetrTransformerEncoder
        #         num_layers=6,
        #         layer_cfg=dict(  # DeformableDetrTransformerEncoderLayer
        #             self_attn_cfg=dict(  # MultiScaleDeformableAttention
        #                 embed_dims=256,
        #                 num_heads=8,
        #                 num_levels=3,
        #                 num_points=4,
        #                 dropout=0.0,
        #                 batch_first=True),
        #             ffn_cfg=dict(
        #                 embed_dims=256,
        #                 feedforward_channels=1024,
        #                 num_fcs=2,
        #                 ffn_drop=0.0,
        #                 act_cfg=dict(type='ReLU', inplace=True)))),
        #     positional_encoding=dict(num_feats=128, normalize=True)),
        enforce_decoder_input_project=False,
        positional_encoding=dict(num_feats=128, normalize=True),
        transformer_decoder=dict(  # Mask2FormerTransformerDecoder
            return_intermediate=True,
            num_layers=9,
            layer_cfg=dict(  # Mask2FormerTransformerDecoderLayer
                self_attn_cfg=dict(  # MultiheadAttention
                    embed_dims=256,
                    num_heads=8,
                    dropout=0.0,
                    batch_first=True),
                cross_attn_cfg=dict(  # MultiheadAttention
                    embed_dims=256,
                    num_heads=8,
                    dropout=0.0,
                    batch_first=True),
                ffn_cfg=dict(
                    embed_dims=256,
                    feedforward_channels=2048,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type='ReLU', inplace=True))),
            init_cfg=None),
        loss_cls=dict(
            type='mmdet.FocalLoss',
            use_sigmoid=True,
            gamma=4.0,
            alpha=0.25,
            loss_weight=1.0),
        # loss_cls=dict(
        #     type='mmdet.CrossEntropyLoss',
        #     use_sigmoid=False,
        #     loss_weight=2.0,
        #     reduction='mean',
        #     class_weight=[1.0] * num_classes + [0.1]), # no need to add 0.1
        loss_mask=dict(
            type='mmdet.CrossEntropyLoss',
            use_sigmoid=True,
            reduction='mean',
            loss_weight=5.0),
        loss_dice=dict(
            type='mmdet.DiceLoss',
            use_sigmoid=True,
            activate=True,
            reduction='mean',
            naive_dice=True,
            eps=1.0,
            loss_weight=5.0),
        assigner_zero_layer_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        assigner_cfg=dict(
                type='mmdet.HungarianAssigner',
                match_costs=[
                        dict(type='mmdet.FocalLossCost', gamma=4.0,alpha=0.25,weight=1.0),
                        dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                        dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                    ]),
        sampler_cfg=dict(type='_MaskPseudoSampler'))
m2f_panoptic_fusion_head=dict(
        type='mmdet.MaskFormerFusionHead',
        num_things_classes=len(thing_class),
        num_stuff_classes=len(stuff_class),
        loss_panoptic=None,
        init_cfg=None)
    
# data_preprocessor = dict(
#     type='mmseg.SegDataPreProcessor',
#     mean=[123.675, 116.28, 103.53],
#     std=[58.395, 57.12, 57.375],
#     bgr_to_rgb=True,
#     pad_val=0,
#     seg_pad_val=255,
#     size=img_scale,
#     test_cfg=dict(size_divisor=32))
model = dict(
    type='_IMAGE_PANOSEG_M2F',
    task_mode=task_mode,
    proj_path=proj_path,
    use_physical_points=True,
    use_img_logits=True,
    data_preprocessor=dict(
        type='_Seg3DDataPreprocessor',
        voxel=False,
        voxel_type='cylindrical',
        return_points=True,
        bgr_to_rgb=True,
        mean=[123.675, 116.28, 103.53], 
        std=[58.395, 57.12, 57.375]
        # voxel_layer=dict(
        #     grid_shape=grid_shape,
        #     point_cloud_range=point_cloud_range_polar,
        #     max_num_points=-1,
        #     max_voxels=-1,),
        ),
    img_backbone=dict(
        type='mmdet.ResNet',
        depth=50,
        # deep_stem=False,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=-1,
        norm_cfg=dict(type='BN', requires_grad=False),
        style='pytorch',
        init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')),
##########################################################
    decode_head=m2f_pano_head,
    # img_head_postprocess=m2f_panoptic_fusion_head,
    train_cfg=dict(
        num_points=12544,
        oversample_ratio=3.0,
        importance_sample_ratio=0.75,
        assigner=dict(
            type='HungarianAssigner',
            match_costs=[
                dict(type='ClassificationCost', weight=2.0),
                dict(
                    type='CrossEntropyLossCost', weight=5.0, use_sigmoid=True),
                dict(type='DiceCost', weight=5.0, pred_act=True, eps=1.0)
            ]),
        sampler=dict(type='MaskPseudoSampler')),
    test_cfg=dict(
        panoptic_on=True,
        # For now, the dataset does not support
        # evaluating semantic segmentation metric.
        semantic_on=False,
        instance_on=True,
        # max_per_image is for instance segmentation.
        max_per_image=100,
        iou_thr=0.8,
        # In Mask2Former's panoptic postprocessing,
        # it will filter mask area where score is less than 0.5 .
        filter_low_score=True),
    feat_dims=feature_map_dims,
    voxel_encoder=dict(
        type='SegVFE',
        feat_channels=[64, 128, 256, 256],
        in_channels=vfe_inchannel, 
        with_voxel_center=True,
        feat_compression=16,
        return_point_feats=False
        ), 
    backbone=dict(
        type='_Asymm3DSpconv',
        grid_size=grid_shape,
        input_channels=16,
        base_channels=32,
        more_conv=True,
        out_channels=256,
        norm_cfg=dict(type='BN1d', eps=1e-5, momentum=0.1)),)
    
##########################################################
# optimizer and scheduler

# v1: original setting for p3former
# lr = 0.0008
# optim_wrapper = dict(
#     type='OptimWrapper',
#     optimizer=dict(type='AdamW', lr=lr, weight_decay=0.01))
# param_scheduler = [
#     dict(
#         type='MultiStepLR',
#         begin=0,
#         end=120,
#         by_epoch=True,
#         milestones=[60, 75, 90],
#         gamma=0.2)
# ]
# v2: setting for image mask2former
embed_multi = dict(lr_mult=1.0, decay_mult=0.0)
optimizer = dict(
    type='AdamW', lr=0.0001, weight_decay=0.05, eps=1e-8, betas=(0.9, 0.999))
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=optimizer,
    clip_grad=dict(max_norm=0.01, norm_type=2),
    paramwise_cfg=dict(
        custom_keys={
            'backbone': dict(lr_mult=0.1, decay_mult=1.0),
            'query_embed': embed_multi, #TODO-YINING: what is this?
            'query_feat': embed_multi,
            'level_embed': embed_multi,
        },
        norm_decay_mult=0.0))
# learning policy
param_scheduler = [
    dict(
        type='PolyLR',
        eta_min=0,
        power=0.9,
        begin=0,
        end=90000,
        by_epoch=False)
]
##########################################################


##########################################################
val_evaluator = dict(
    type='_PanopticSegMetric_MM',
    modality='img', # mm, pts, img
    thing_class_inds=thing_class, 
    stuff_class_inds=stuff_class,
    min_num_points=3,
    id_offset=seg_offset,
    dataset_type='nuscenes',
    learning_map_inv=dict({ #TODO-YINING: only for semantickitti
        0: 10,
        1: 11,
        2: 15,
        3: 18,
        4: 20,
        5: 30,
        6: 31,
        7: 32,
        8: 40,
        9: 44,
        10: 48,
        11: 49,
        12: 50,
        13: 51,
        14: 70,
        15: 71,
        16: 72,
        17: 80,
        18: 81,
        19: 0
    }))
test_evaluator = val_evaluator
##########################################################

##########################################################
# VISUALIZATION BACKEND
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')
##########################################################

##########################################################
# HOOKS
default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=50, log_metric_by_epoch=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(
        type='CheckpointHook', 
        by_epoch=True, 
        interval=1,
        # interval=5000,
        save_best='img_pq',
        greater_keys=['pts_pq', 'img_pq']), #TODO-YINING: check it, https://github.com/open-mmlab/mmengine/blob/39ed23fae87267de8ef044a94a597a6904fe7f85/docs/zh_cn/tutorials/hook.md?plain=1#L117
    sampler_seed=dict(type='DistSamplerSeedHook'),
    # visualization=dict(type='SegVisualizationHook') #TODO-YINING: not registered
    )
##########################################################

# Default setting for scaling LR automatically
#   - `enable` means enable scaling LR automatically
#       or not by default.
#   - `base_batch_size` = (8 GPUs) x (2 samples per GPU).
auto_scale_lr = dict(enable=False, base_batch_size=16)

##########################################################
# CUSTOM IMPORTS #TODO: Clean up
custom_imports = dict(
    imports=[
        'p3former.backbones.cylinder3d',
        'p3former.backbones.swiftnet',
        'p3former.backbones.imgseghead',
        'p3former.data_preprocessors.data_preprocessor',
        'p3former.decode_heads.p3former_head',
        # 'p3former.decode_heads.image_pano_head',
        'mask2former.decode_heads.poi_mask2former_head',
        'p3former.segmentors.p3former',
        'p3former.segmentors.image_panoseg_m2f',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'evaluation.metrics.panoptic_seg_metric_mm',
        'datasets.nuscenes_dataset_seg',
        'datasets.transforms.loading',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
        'hooks.prediction_hook',
    ],
    allow_failed_imports=False)