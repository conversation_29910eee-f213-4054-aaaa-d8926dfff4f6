_base_ = [
    '../IAL_Xload.py'
]
find_unused_parameters=True

##########################################################
# DATA PATHS: CHANGE THESE VALUES TO YOUR FOLDERSdevice='HPC5'
device='HPC3'
if device == 'HPC5':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'HPC3':
    proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
elif device == 'HPC1':
    proj_path='/mnt/data/data/yining/codefield/3dmmpano_240109/'
elif device == 'HPC4':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'ASTAR':
    proj_path = '/home/<USER>/codefield/3dmmpano/'
elif device == 'AutoDL':
    proj_path = '/root/autodl-tmp/3dmmpano/'
elif device == 'NSCC':
    proj_path = '/home/<USER>/astar/ares/qianp/scratch/yining/codefield/3dmmpano/'
else:
    proj_path = '.'
    
dataset_type = 'NuScenesSegDataset'
data_root = proj_path+'data/nuscenes_full/'
data_prefix = dict(
    pts='samples/LIDAR_TOP',
    pts_semantic_mask='',
    # pts_semantic_mask='lidarseg/v1.0-trainval',
    pts_panoptic_mask='',
    # pts_panoptic_mask='lidarseg/v1.0-trainval',
    CAM_FRONT='samples/CAM_FRONT',
    CAM_FRONT_LEFT='samples/CAM_FRONT_LEFT',
    CAM_FRONT_RIGHT='samples/CAM_FRONT_RIGHT',
    CAM_BACK='samples/CAM_BACK',
    CAM_BACK_RIGHT='samples/CAM_BACK_RIGHT',
    CAM_BACK_LEFT='samples/CAM_BACK_LEFT')
##########################################################

##########################################################
# SETTINGS FOR 3D  
if dataset_type=='NuScenesSegDataset':
    point_cloud_range = [-50.0, -50.0, -5.0, 50.0, 50.0, 3.0]
    point_cloud_range_polar = [0, -3.14159265359, -5., 50.0, 3.14159265359, 3.]
    all_class = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]
    thing_class = [1,2,3,4,5,6,7,8,9,10]
    stuff_class = [11,12,13,14,15,16]
    ignore_index = 0
    num_classes = 17

    seg_offset = 1000
else:
    raise NotImplementedError()

##########################################################
# SETTINGS FOR 2D
img_scale = (640, 360) # *0.4
##########################################################

##########################################################
# SETTINGS FOR IAL
grid_shape = [480, 360, 32] 
cal_PQG_recall = True
use_2d_pred_query = True
use_3d_pred_query = True
share_pe_layer = False #TODO: check the result
sample_amount = 256 # should larger or equal to sample_amount_3d
sample_amount_PQG = 128
cand_2d_folder = 'sam_default/pred_inst_pts_v4_0906'
inst_3d_params = dict(
                    sampling_method='fps', # 'fps' or 'nms'
                    sel_para=dict(
                        kernel_size=3,
                        hm_thre=False, 
                        use_voxel_center=True, 
                    ),
                    nms_para_3d=dict(
                        K=sample_amount_PQG,
                        dim='bev',
                        dist_thresh=0.7,
                        adapt_thresh=True,
                        vote_thresh=0.1,
                        score_thresh=0.1,
                        pts_range_polar=point_cloud_range_polar,
                        pts_range_cart=point_cloud_range,
                        grid_size=grid_shape
                        ),
                    nms_para_fuse=dict(
                        K=sample_amount_PQG,
                        dim='bev',
                        dist_thresh=0.5,
                        adapt_thresh=False,
                        vote_thresh=0.1,
                        score_thresh=0.1,
                        pts_range_polar=point_cloud_range_polar,
                        pts_range_cart=point_cloud_range,
                        grid_size=grid_shape
                        ),
                    q_fusion_mode='default', # 'default' or 'E3D' 
                    NMS_mode='bev', # 'bev' or 'polar+height'
                    heatmap_branch='transfusion', # 'transfusion' or 'lcps'
                    heatmap_detach=True,
                    inst_select_mode='agnostic', # 'class' or 'agnostic' or 'GT'
                    GT_params=None,         # for GT, whether to use GT coor with noise 
                    query_content='mlp',    # 'zero'-> zero init; 'rand'-> random init; 'avg'-> ?; 'lidar_feat'-> select from lidar feature; 'mm_feat'-> select from multimodal feature
                    query_PE='centeriod+voxelscale',       # ‘centeriod’ or ‘centeriod+voxelscale’ or 'SPE(align to token PE)' or 'none'
                    share_pe_layer=share_pe_layer,
                    height_pred='none',
                    shape_pad='rand',
                    hm_sigma=3.,           # a number or 'adaptive'
                    hm_mode='bin',      # 'bin' or 'class'
                    offset=False,            # whether to use offset
                    height=False,           # whether to use height
                    fog=True,               # whether to use fog
                    sigmoid_fog=False,      # whether to use sigmoid for fog
                    fog_branch='transfusion',       # 'none' or 'transfusion' or 'lcps'
                    fog_detach=True,
                    loss_heatmap=dict(type='mmdet.MSELoss', loss_weight=0.1), # 100？
                    loss_offset=dict(type='mmdet.L1Loss', loss_weight=0.1), # 10
                    loss_fog=dict(type='mmdet.CrossEntropyLoss', loss_weight=0.01), # 1
                    loss_height=dict(type='mmdet.MSELoss', loss_weight=0.01), # 10
                    ) 

##########################################################

##########################################################
# DATALOADER: Remember to check batch size and num_worker
# pre_transform_combo = [
#     dict(
#         type='_LoadAnnotations3D_UDA',
#         UDA_task_name='city',
#         with_tgt_PL=True,
#         with_2d_pred_query=use_2d_pred_query,
#         cand_2d_folder=cand_2d_folder, 
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.uint8',
#         seg_offset=seg_offset,
#         dataset_type='nuscenes'),
# ]
# train_pipeline = [
#     dict(
#         type='_LoadAnnotations3D_UDA', 
#         UDA_task_name='city',
#         with_tgt_PL=True,
#         with_2d_pred_query=use_2d_pred_query,
#         cand_2d_folder=cand_2d_folder, 
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.uint8',
#         seg_offset=seg_offset,
#         dataset_type='nuscenes'),
# ]
train_dataloader = dict(
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        # ann_file='nuscenes_infos_train_1.pkl',
        ann_file='pkl_files_UDA/nuscenes_infos_train_SG.pkl',
        # pipeline=train_pipeline,
        # metainfo=metainfo,
        # modality=input_modality,
        test_mode=False),
)

# test_pipeline = [
#     dict(
#         type='_LoadAnnotations3D_UDA', 
#         UDA_task_name='city',
#         with_tgt_PL=True,
#         with_2d_pred_query=use_2d_pred_query,
#         cand_2d_folder=cand_2d_folder, 
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.uint8',
#         seg_offset=seg_offset,
#         dataset_type='nuscenes'),
#     dict(type='_PointSegClassMapping', ),
# ]
test_dataloader = dict(
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        # ann_file='nuscenes_infos_train_1.pkl',
        ann_file='pkl_files_UDA/nuscenes_infos_val_SG.pkl',
        # pipeline=test_pipeline,
        # modality=input_modality,
        test_mode=True)
)

val_dataloader = test_dataloader
val_dataloader = test_dataloader
##########################################################

##########################################################
# MODEL
model = dict(
    use_img_logits=True,   # infer 2D logits in segmentor_soft.py
    data_preprocessor=dict(
        type='_Seg3DDataPreprocessor_UDA',
        use_tgt_pl=True,
        # filter_aug_pts=False,
        # voxel=True,
        # voxel_type='cylindrical',
        # inst_3d_params=inst_3d_params,
        # inst_labels=thing_class,
        # grid_shape=grid_shape,
        # return_points=True,
        # voxel_layer=dict(
        #     grid_shape=grid_shape,
        #     point_cloud_range=point_cloud_range_polar,
        #     max_num_points=-1,
        #     max_voxels=-1,),
    ),
    decode_head=dict(
        type='_IALHead_xMUDA',
        use_xMUDA=True,
        use_tgt_pl=True,
        ###### disable inst branch loss functions ######
        loss_ce=dict(type='mmdet.CrossEntropyLoss', use_sigmoid=False, class_weight=None, loss_weight=1.0),     # stuff sem head
        loss_lovasz=dict(type='LovaszLoss', reduction='none', loss_weight=1.0), # stuff sem head
        loss_cls=dict(type='mmdet.FocalLoss', use_sigmoid=True, gamma=4.0, alpha=0.25, loss_weight=1.0),
        loss_mask=dict(type='mmdet.FocalLoss', use_sigmoid=True, gamma=2.0, alpha=0.25, reduction='mean', loss_weight=1.0),
        loss_dice=None, # dict(type='mmdet.DiceLoss', loss_weight=2.0),
        loss_imgseg_lv=dict(type='LovaszLoss', reduction='none', loss_weight=1.0),
        loss_imgseg_ce=dict(type='mmdet.CrossEntropyLoss', use_sigmoid=False, class_weight=None, loss_weight=1.0),
        ################################################
        img_feat_dim=128,
        lambda_xm=1.0,  #TODO-YINING: change this value when fine-tuning on target domain
        task_name='city', #TODO-YINING: change this value when training on different domain task
    ),
    img_head=dict(         # 2D head
        type='ImgSegHead_BNReLU',
        in_features=128,
        num_classes=num_classes,
        upsample_logits=True, 
        upsample_size=(img_scale[1], img_scale[0])), # [360, 640]
)
##########################################################

##########################################################
# CUSTOM IMPORTS
custom_imports = dict(
    imports=[
        'p3former.backbones.imgseghead',
        'p3former.backbones.cylinder3d_hmm',
        'p3former.backbones.swiftnet',
        'p3former.data_preprocessors.data_preprocessor_hm',
        'p3former.data_preprocessors.data_preprocessor_UDA',
        'p3former.decode_heads.IAL_xMUDA_head',
        'p3former.decode_heads.heatmap_head',
        'p3former.segmentors.p3former_hm',
        'p3former.segmentors.p3former_mm_soft',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.nuscenes_dataset_seg',
        'datasets.transforms.loading',
        'datasets.transforms.loading_UDA',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
        'hooks.prediction_hook',
    ],
    allow_failed_imports=False)
##########################################################
