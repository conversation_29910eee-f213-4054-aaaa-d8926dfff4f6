_base_ = [
    '../IAL.py'
]
# find_unused_parameters=True
##########################################################
# DATA PATHS: CHANGE THESE VALUES TO YOUR FOLDERSdevice='HPC5'
device='HPC3'
if device == 'HPC5':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'HPC3':
    proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
elif device == 'HPC1':
    proj_path='/mnt/data/data/yining/codefield/3dmmpano_240109/'
elif device == 'HPC4':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'ASTAR':
    proj_path = '/home/<USER>/codefield/3dmmpano/'
elif device == 'AutoDL':
    proj_path = '/root/autodl-tmp/3dmmpano/'
elif device == 'NSCC':
    proj_path = '/home/<USER>/astar/ares/qianp/scratch/yining/codefield/3dmmpano/'
else:
    proj_path = '.'
    
dataset_type = 'NuScenesSegDataset'
data_root = proj_path+'data/nuscenes_full/'
data_prefix = dict(
    pts='samples/LIDAR_TOP',
    pts_semantic_mask='',
    # pts_semantic_mask='lidarseg/v1.0-trainval',
    pts_panoptic_mask='',
    # pts_panoptic_mask='lidarseg/v1.0-trainval',
    CAM_FRONT='samples/CAM_FRONT',
    CAM_FRONT_LEFT='samples/CAM_FRONT_LEFT',
    CAM_FRONT_RIGHT='samples/CAM_FRONT_RIGHT',
    CAM_BACK='samples/CAM_BACK',
    CAM_BACK_RIGHT='samples/CAM_BACK_RIGHT',
    CAM_BACK_LEFT='samples/CAM_BACK_LEFT')
##########################################################

##########################################################
# SETTINGS FOR 3D  
if dataset_type=='NuScenesSegDataset':
    num_classes = 17
else:
    raise NotImplementedError()

##########################################################
# SETTINGS FOR 2D
img_scale = (640, 360) # *0.4
##########################################################

##########################################################
# DATALOADER: Remember to check batch size and num_worker
train_dataloader = dict(
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        # ann_file='nuscenes_infos_train_1.pkl',
        ann_file='pkl_files_UDA/nuscenes_infos_train_day.pkl',
        # pipeline=train_pipeline,
        # metainfo=metainfo,
        # modality=input_modality,
        test_mode=False),
)


test_dataloader = dict(
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        # ann_file='nuscenes_infos_train_1.pkl',
        ann_file='pkl_files_UDA/nuscenes_infos_val_night.pkl',
        # pipeline=test_pipeline,
        # modality=input_modality,
        test_mode=True)
)


val_dataloader = test_dataloader
##########################################################

##########################################################
# MODEL
model = dict(
    use_img_logits=True,   # infer 2D logits in segmentor_soft.py
    decode_head=dict(
        type='_IALHead_xMUDA',
        use_xMUDA=True,
        use_tgt_pl=False,
        #TODO-YINING: choose which losses to use
        # loss_ImgSemSeg=dict(   # same as 3D mask loss 
        #     type='mmdet.FocalLoss',
        #     use_sigmoid=True,
        #     gamma=2.0,
        #     alpha=0.25,
        #     reduction='mean',
        #     loss_weight=1.0),
        # loss_imgseg_ce=dict(
        #     type='mmdet.CrossEntropyLoss',
        #     use_sigmoid=False,
        #     class_weight=None,
        #     loss_weight=1.0),
        # loss_imgseg_lv=dict(type='LovaszLoss',
        #     reduction='none',),
        loss_ce=dict(type='mmdet.CrossEntropyLoss', use_sigmoid=False, class_weight=None, loss_weight=1.0),     # stuff sem head
        loss_lovasz=dict(type='LovaszLoss', reduction='none', loss_weight=1.0), # stuff sem head
        loss_cls=dict(type='mmdet.FocalLoss', use_sigmoid=True, gamma=4.0, alpha=0.25, loss_weight=1.0),
        loss_mask=dict(type='mmdet.FocalLoss', use_sigmoid=True, gamma=2.0, alpha=0.25, reduction='mean', loss_weight=1.0),
        loss_dice=dict(type='mmdet.DiceLoss', loss_weight=2.0),
        loss_imgseg_lv=dict(type='LovaszLoss', reduction='none', loss_weight=1.0),
        loss_imgseg_ce=dict(type='mmdet.CrossEntropyLoss', use_sigmoid=False, class_weight=None, loss_weight=1.0),
        img_feat_dim=128,
        lambda_xm=1.0,  #TODO-YINING: change this value when fine-tuning on target domain
        task_name='light', #TODO-YINING: change this value when training on different domain task
    ),
    img_head=dict(         # 2D head
        type='ImgSegHead_BNReLU',
        in_features=128,
        num_classes=num_classes,
        upsample_logits=True, 
        upsample_size=(img_scale[1], img_scale[0])), # [360, 640]
)
##########################################################

##########################################################
# CUSTOM IMPORTS
custom_imports = dict(
    imports=[
        'p3former.backbones.imgseghead',
        'p3former.backbones.cylinder3d_hmm',
        'p3former.backbones.swiftnet',
        'p3former.data_preprocessors.data_preprocessor_hm',
        'p3former.decode_heads.IAL_xMUDA_head',
        'p3former.decode_heads.heatmap_head',
        'p3former.segmentors.p3former_hm',
        'p3former.segmentors.p3former_mm_soft',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.nuscenes_dataset_seg',
        'datasets.transforms.loading',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
        'hooks.prediction_hook',
    ],
    allow_failed_imports=False)
##########################################################
