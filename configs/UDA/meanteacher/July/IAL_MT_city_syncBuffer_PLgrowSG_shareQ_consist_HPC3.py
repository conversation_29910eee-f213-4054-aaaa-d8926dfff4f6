_base_ = [
    '../../../_base_/default_runtime.py'
]

model_wrapper_cfg = dict(type='MMDistributedDataParallel_MT') #MT-1

##########################################################
seed = 133365207 # have good performance on sub-set of nuScenes
# deterministic = False
randomness=dict(seed=seed)
##########################################################

##########################################################
# DATA PATHS: CHANGE THESE VALUES TO YOUR FOLDERSdevice='HPC5'
device='HPC3'
if device == 'HPC5':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'HPC3':
    proj_path='/home/<USER>/codefield/misc/3dmmpano_1015/'
elif device == 'HPC1':
    proj_path='/mnt/data/data/yining/codefield/3dmmpano_240109/'
elif device == 'HPC4':
    proj_path='/mnt/data/users/yining.pan/codefield/3dmmpano/'
elif device == 'ASTAR':
    proj_path = '/home/<USER>/codefield/3dmmpano/'
elif device == 'AutoDL':
    proj_path = '/root/autodl-tmp/3dmmpano/'
elif device == 'NSCC':
    proj_path = '/home/<USER>/astar/ares/qianp/scratch/yining/codefield/3dmmpano/'
else:
    proj_path = '.'
    
dataset_type = 'NuScenesSegDataset'
# nclasses=17 # noise, 10 thing classes + 6 stuff classes
task_mode='train'
data_root = proj_path+'data/nuscenes_full/'
data_prefix = dict(
    pts='samples/LIDAR_TOP',
    pts_semantic_mask='',
    # pts_semantic_mask='lidarseg/v1.0-trainval',
    pts_panoptic_mask='',
    # pts_panoptic_mask='lidarseg/v1.0-trainval',
    CAM_FRONT='samples/CAM_FRONT',
    CAM_FRONT_LEFT='samples/CAM_FRONT_LEFT',
    CAM_FRONT_RIGHT='samples/CAM_FRONT_RIGHT',
    CAM_BACK='samples/CAM_BACK',
    CAM_BACK_RIGHT='samples/CAM_BACK_RIGHT',
    CAM_BACK_LEFT='samples/CAM_BACK_LEFT')
# spg_prefix=proj_path+'data/semantickitti/superpoint_graph'
mode='train' # train or test, used to determine sample_point
##########################################################

##########################################################
# sync_bn = 'mmcv' # 'torch' or 'mmcv' or None # use SyncBN to avoid multi-gpu training instability, especially for small batch size on A5000
enable_sync_bn = False #SyncBN-1
if enable_sync_bn:
    assert enable_sync_bn and device=='ASTAR', 'SyncBN only support ASTAR'
    vfe_norm_cfg = dict(type='SyncBN', eps=1e-3, momentum=0.01)
    c3d_norm_cfg = dict(type='SyncBN', eps=1e-5, momentum=0.1)
else:
    vfe_norm_cfg = dict(type='BN1d', eps=1e-3, momentum=0.01)
    c3d_norm_cfg = dict(type='BN1d', eps=1e-5, momentum=0.1)
##########################################################

##########################################################
# IMG AUGMENTATION
img_aug = True
##########################################################


##########################################################
# image related settings
img_scale = (640, 360) # *0.4
img_norm_cfg = dict(mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)
# img_norm_cfg = dict(
#     mean=[103.530, 116.280, 123.675], std=[57.375, 57.120, 58.395], to_rgb=False)
resnet18_path = 'checkpoints/resnet18-5c106cde.pth'
feature_map_dims = [128] # for resnet18: [512, 1024, 2048]
##########################################################

##########################################################
# SETTINGS FOR POINTS  
use_intensity = True
vfe_inchannel = 6 if use_intensity else 5
lidar_dim = 4 if use_intensity else 3

if dataset_type=='NuScenesSegDataset':
    point_cloud_range = [-50.0, -50.0, -5.0, 50.0, 50.0, 3.0]
    point_cloud_range_polar = [0, -3.14159265359, -5., 50.0, 3.14159265359, 3.]
    all_class = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]
    thing_class = [1,2,3,4,5,6,7,8,9,10]
    stuff_class = [11,12,13,14,15,16]
    ignore_index = 0
    num_classes = 17
    
    seg_offset = 1000
else:
    raise NotImplementedError()


# Voxelization
grid_shape = [480, 360, 32] # <-- 


# Visualization
if mode == 'test':
    save_semantic=True
else:
    save_semantic=False

backend_args = None  # storage backend
##########################################################


##########################################################
# SETTINGS FOR QUERY GENERATION

#semantic query
sem_query_feat = 'lidar_feat'
decoupled_stuff_query=False

# query update
q_pe_update = False #TODO: check the result
share_pe_layer = False #TODO: check the result

# candidate generation
cal_PQG_recall = False
use_2d_pred_query = True
use_3d_pred_query = True
sample_amount = 256 # should larger or equal to sample_amount_3d
sample_amount_PQG = 128
cand_2d_folder = 'sam_default/pred_inst_pts_v4_0906'
inst_3d_params = dict(
                    sampling_method='fps', # 'fps' or 'nms'
                    # K=sample_amount_PQG,
                    sel_para=dict(
                        kernel_size=3,
                        hm_thre=False, 
                        use_voxel_center=True, 
                    ),
                    nms_para_3d=dict(
                        K=sample_amount_PQG,
                        dim='bev',
                        dist_thresh=0.7,
                        adapt_thresh=True,
                        vote_thresh=0.1,
                        score_thresh=0.1,
                        pts_range_polar=point_cloud_range_polar,
                        pts_range_cart=point_cloud_range,
                        grid_size=grid_shape
                        ),
                    nms_para_fuse=dict(
                        K=sample_amount_PQG,
                        dim='bev',
                        dist_thresh=0.5,
                        adapt_thresh=False,
                        vote_thresh=0.1,
                        score_thresh=0.1,
                        pts_range_polar=point_cloud_range_polar,
                        pts_range_cart=point_cloud_range,
                        grid_size=grid_shape
                        ),
                    q_fusion_mode='default', # 'default' or 'E3D' 
                    NMS_mode='bev', # 'bev' or 'polar+height'
                    heatmap_branch='transfusion', # 'transfusion' or 'lcps'
                    heatmap_detach=True,
                    inst_select_mode='agnostic', # 'class' or 'agnostic' or 'GT'
                    # sample_amount=sample_amount_3d, # samples per class
                    GT_params=None,         # for GT, whether to use GT coor with noise 
                    query_content='mlp',    # 'zero'-> zero init; 'rand'-> random init; 'avg'-> ?; 'lidar_feat'-> select from lidar feature; 'mm_feat'-> select from multimodal feature
                    query_PE='centeriod+voxelscale',       # ‘centeriod’ or ‘centeriod+voxelscale’ or 'SPE(align to token PE)' or 'none'
                    share_pe_layer=share_pe_layer,
                    height_pred='none',
                    shape_pad='rand',
                    hm_sigma=3.,           # a number or 'adaptive'
                    hm_mode='bin',      # 'bin' or 'class'
                    offset=False,            # whether to use offset
                    height=False,           # whether to use height
                    fog=True,               # whether to use fog
                    sigmoid_fog=False,      # whether to use sigmoid for fog
                    fog_branch='transfusion',       # 'none' or 'transfusion' or 'lcps'
                    fog_detach=True,
                    loss_heatmap=dict(type='mmdet.MSELoss', loss_weight=0.1), # 100？
                    loss_offset=dict(type='mmdet.L1Loss', loss_weight=0.1), # 10
                    loss_fog=dict(type='mmdet.CrossEntropyLoss', loss_weight=0.01), # 1
                    loss_height=dict(type='mmdet.MSELoss', loss_weight=0.01), # 10
                    ) 
heatmap_head=dict( 
        type='Heatmap_Head',
        input_dim=64,
        output_dim=len(thing_class) if inst_3d_params['hm_mode']=='class' else 1,
    ) if inst_3d_params['heatmap_branch']=='lcps' else None
# task_mode='debug'
##########################################################


##########################################################
# SETTINGS FOR MULTI-MODAL FUSION 
share_featfusion_layer = False #NOTE: True's result is not good
share_mpe_layer = True #NOTE: #TODO: check the result 
Efficient_MaskUpdate = False

fusion_mode='centeroid'
p_fusion_mode='max'
fuse_params = {
            'voxel_pe_type': 'polar+cart', # 'cart' or 'polar' or 'polar+cart'
            'voxel_pe_param': 'centeriod+scale', # 'centeriod' or 'centeriod+corner'
            'voxel_pe_aggregate': 'avg', # 'max' or 'avg' or 'set'
            'image_pe_param': 'same_as_lidar', # 'physical' or ‘physical+centeriod+scale’ or 'same_as_lidar'
            'image_pe_aggregate': 'avg', # 'max' or 'avg' or 'set' if 'image_pe_param'=='physical' else 'None'  
            'mask_update_mode':  'mlp', # 'mlp' or 'cross_attn' or 'deformable_cross_attn'
            'query_update_mode': 'mlp', # 'concat' or 'cross_attn' or 'deformable_cross_attn' or 'default (here feature is mm_feature already processed in mask_update)'
        }      
##########################################################


##########################################################
# DATASET CONFIG: nuScenes 
if dataset_type == 'NuScenesSegDataset':
    class_names = [
        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
    ]
else:
    raise NotImplementedError()

metainfo = dict(classes=class_names)

input_modality = dict( # <-- not used
    use_lidar=True,
    use_camera=True,
    use_radar=False,
    use_map=False,
    use_external=False)


# TODO: Check this
# pre_transform = [
#     dict(
#         type='LoadPointsFromFile',
#         coord_type='LIDAR',
#         load_dim=4,
#         use_dim=4,
#         backend_args=backend_args),
#     dict(
#         type='_LoadAnnotations3D',
#         with_bbox_3d=False,
#         with_label_3d=False,
#         with_panoptic_3d=True,
#         seg_3d_dtype='np.int32',
#         seg_offset=2**16,
#         dataset_type='semantickitti',
#         backend_args=backend_args),
#     dict(type='PointSegClassMapping', )]

##########################################################
# FileNotFoundError: [Errno 2] No such file or directory: 'panoptic/v1.0-mini/9d9bf11fb0e144c8b446d54a8a00184f_panoptic.npz'

##########################################################
# DATA LOADING PIPELINES
albu_transforms = [
    dict(
    type='RandomBrightnessContrast',
    brightness_limit=[-0.5, 0.],
    contrast_limit=[0.1, 0.3],
    p=1.),]
pre_transform_combo = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim),
    dict(
        type='_LoadViewImage_Mask',
        fusion_mode=fusion_mode,
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    dict(type='_ImageyNormalize', **img_norm_cfg),
    dict(
        type='_LoadAnnotations3D', # for nuScenes
        with_2d_pred_query=use_2d_pred_query,
        cand_2d_folder=cand_2d_folder, 
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes'),
    dict(type='_PointSegClassMapping')
]
train_pipeline_noaug = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    # # dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    dict(type='_ImageyNormalize', **img_norm_cfg),
    # dict(type='_ImagePad', size_divisor=32),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_2d_pred_query=use_2d_pred_query,
        cand_2d_folder=cand_2d_folder, 
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    # dict(type='PointSample', num_points=-1, sample_range=40.0),
    # dict(
    #     type='RandomChoice',
    #     transforms=[
    #         [
    #             dict(
    #                 type='_LaserMix_MM_IMG',
    #                 img_aug=img_aug,
    #                 num_areas=[3, 4, 5, 6],
    #                 pitch_angles=[-30, 10],
    #                 pre_transform=[*pre_transform_combo],
    #                 prob=0.5)
    #         ],
    #         [
    #             dict(
    #                 type='_PolarMix_MM_IMG',
    #                 img_aug=img_aug,
    #                 instance_classes=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    #                 swap_ratio=0.5,
    #                 rotate_paste_ratio=1.0,
    #                 pre_transform=[*pre_transform_combo],
    #                 prob=0.5)
    #         ],
    #     ],
    #     prob=[0.2, 0.8]),
    dict(
        type='RandomFlip3D_MM',
        ),
    dict(
        type='_GlobalRotScaleTransAll',
        rot_range=[-0.78539816, 0.78539816],
        scale_ratio_range=[0.95, 1.05],
        translation_std=[0.1, 0.1, 0.1],
    ),

    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img', 
                   'pcd_rotation', 'pcd_scale_factor', 'pcd_trans', 'lidar_aug_matrix', 'img_aug_matrix', 'scale_factor']
        )
#     dict(type='_Pack3DDetInputs', 
#         keys=['points', 'pts_semantic_mask', 'pts_instance_mask'],
#         meta_keys=['lidar2img']
#         )
]
                              
test_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    # # dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadViewImage_Mask',
        # img_seg=True,
        fusion_mode=fusion_mode,
        ),
    dict(type='_ImageResize', img_scale=img_scale, keep_ratio=True),
    dict(type='_ImageyNormalize', **img_norm_cfg),
    # dict(type='_ImagePad', size_divisor=32),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_2d_pred_query=use_2d_pred_query,
        cand_2d_folder=cand_2d_folder,
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img', 
                   'pcd_rotation', 'pcd_scale_factor', 'pcd_trans', 'lidar_aug_matrix', 'img_aug_matrix', 'scale_factor']
        )
    # dict(type='_Pack3DDetInputs', 
    #      keys=['points', 'pts_semantic_mask', 'pts_instance_mask'])
    
]

# construct a pipeline for data and gt loading in show function
# please keep its loading function consistent with test_pipeline (e.g. client)
eval_pipeline = [
    dict(
        type='LoadPointsFromFile',
        coord_type='LIDAR',
        load_dim=5,
        use_dim=lidar_dim,
        backend_args=backend_args),
    dict(type='LoadMultiViewImageFromFiles'),
    dict(
        type='_LoadAnnotations3D', # P3Former modified loading method
        with_2d_pred_query=use_2d_pred_query,
        cand_2d_folder=cand_2d_folder,
        with_bbox_3d=False,
        with_label_3d=False,
        with_panoptic_3d=True,
        seg_3d_dtype='np.uint8',
        seg_offset=seg_offset,
        dataset_type='nuscenes',
        backend_args=backend_args),
    dict(type='_PointSegClassMapping', ),
    dict(type='Pack3DMMInputs', 
        keys=['img', 'points', 'pts_semantic_mask', 'pts_instance_mask'],
        meta_keys=['lidar2img']
        )
    # dict(type='_Pack3DDetInputs', 
    #      keys=['points', 'pts_semantic_mask', 'pts_instance_mask'])
    
]
##########################################################


##########################################################
# DATALOADER: Remember to check batch size and num_worker
# different settings for source and target dataset

## Pseudo Label Refinement
PL_refine_grow = True
if PL_refine_grow:
    PL_refine_grow = [
        dict(
            type='PLGrow_by_SAMGeo',
        )
    ]
else:
    PL_refine_grow = []
    
PL_refine_add = False
if PL_refine_add:
    PL_refine_augment = [
        dict(
            type='PLRefineAdd',
            img_aug=img_aug,
            stuff_classes=stuff_class,
            ignore_classes=ignore_index,
        )
    ]
else:
    PL_refine_augment = []
        
    
CONSISTENCY_LOSS = True
train_pipeline_pieaug = train_pipeline_noaug.copy()
train_pipeline_pieaug.insert(-3, 
    dict(
        type='RandomChoice',
        transforms=[
            [
                dict(
                    type='_LaserMix_MM_IMG',
                    img_aug=img_aug,
                    num_areas=[3, 4, 5, 6],
                    pitch_angles=[-30, 10],
                    pre_transform=[*pre_transform_combo],
                    prob=0.5)
            ],
            [
                dict(
                    type='_PolarMix_MM_IMG',
                    img_aug=img_aug,
                    instance_classes=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                    swap_ratio=0.5,
                    rotate_paste_ratio=1.0,
                    pre_transform=[*pre_transform_combo],
                    prob=0.5)
            ],
        ],
        prob=[0.2, 0.8]))

if CONSISTENCY_LOSS:
    train_pipeline_weakaug = train_pipeline_noaug.copy()
    train_pipeline_strongaug = [dict(type='SimpleAlbu', transforms=albu_transforms)]
else:
    train_pipeline_weakaug = train_pipeline_pieaug.copy()
    train_pipeline_strongaug = False
              
dataset_basic=dict( #MT-2
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        # ann_file='nuscenes_infos_train.pkl',
        # pipeline=train_pipeline,
        test_mode=False)

dataset_source=dataset_basic.copy()
dataset_source.update(
    pipeline=train_pipeline_pieaug,
)
dataset_source.update(
        ann_file='pkl_files_UDA/nuscenes_infos_train_USA.pkl',
        # ann_file='nuscenes_infos_train_1.pkl', #TODO-YINING: change to UDA pkls
)
dataset_target=dataset_basic.copy()
dataset_target.update(
    pipeline=train_pipeline_weakaug,
)
dataset_target.update(
        ann_file='pkl_files_UDA/nuscenes_infos_train_SG.pkl',
        # ann_file='nuscenes_infos_train_1.pkl', #TODO-YINING: change to UDA pkls
)

train_dataloader = dict(
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type='SemiDataset',  #MT-3
        datasets=[
            dataset_source, 
            dataset_target
        ]
    )
)

# V1: eval both source & target -> current code will cal source & target data together
# test_dataloader = dict( # only target data
#     batch_size=1,
#     num_workers=1,
#     persistent_workers=True,
#     sampler=dict(type='DefaultSampler', shuffle=False),
#     dataset=dict(
#         type='SemiDataset',  
#         datasets=[
#             dataset_source,
#             dataset_target
#         ],
#     )
# )

# V2: eval only target data
test_dataloader = dict( # only target data
    batch_size=2,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        data_prefix=data_prefix,
        ann_file='pkl_files_UDA/nuscenes_infos_val_SG.pkl', #MT-4
        # ann_file='nuscenes_infos_train_1.pkl',
        pipeline=test_pipeline,
        # modality=input_modality,
        test_mode=True)
)

val_dataloader = test_dataloader
##########################################################


##########################################################
# TRAIN, VALIDATION, TEST LOOPS
epoch_length = 15695 #MT-5
num_epoch = 30
train_cfg = dict(type='IterBasedTrainLoop', max_iters=epoch_length*num_epoch, val_interval=int(epoch_length/10))
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')
log_processor = dict(type='LogProcessor', window_size=50, by_epoch=False)
##########################################################


##########################################################
# MODEL
data_preprocessor=dict(
            type='_Seg3DDataPreprocessor_UDA',
            filter_aug_pts=not img_aug,
            voxel=True,
            voxel_type='cylindrical',
            inst_3d_params=inst_3d_params,
            # use_heatmap=inst_3d_params['hm_mode'],
            inst_labels=thing_class,
            # hm_sigma=inst_3d_params['hm_sigma'],
            grid_shape=grid_shape,
            return_points=True,
            voxel_layer=dict(
                grid_shape=grid_shape,
                point_cloud_range=point_cloud_range_polar,
                max_num_points=-1,
                max_voxels=-1,
            ),
            batch_augments=[]
            )
model = dict(
    type = 'IAL_MT',
    ema_decay = 0.99, #TODO-YINING: check this value
    data_preprocessor = data_preprocessor,
    student_aux_aug = train_pipeline_strongaug,
    dn_pl_grow = PL_refine_grow,
    dn_pl_aug = PL_refine_augment,
    student_ckpt = 'work_dirs/IAL_city_source-only/epoch_47.pth',
    student_cfg = dict( #MT-6
        type='_P3Former_SOFT_HM',
        data_preprocessor=data_preprocessor,
        enable_sync_bn=enable_sync_bn, #SyncBN-4, enable for heatmap branch, image branch
        use_3d_pred_query=use_3d_pred_query,
        inst_3d_params=inst_3d_params,
        save_heatmap=save_semantic,
        heatmap_head=heatmap_head,
        task_mode=task_mode,
        proj_path=proj_path,
        use_physical_points=True,
        use_intensity=use_intensity,
        p_fusion_mode=p_fusion_mode,
        filter_aug_pts=not img_aug,
        img_backbone=dict(
            type='SwiftNetResNet', 
            layers=[2, 2, 2, 2],
            num_feature=(128, 128, 128),
            build_decoder=True,
            init_cfg=dict(
                type='Pretrained',
                checkpoint=resnet18_path)),  # download from https://download.pytorch.org/models/resnet18-5c106cde.pth
        feat_dims=feature_map_dims,
        voxel_encoder=dict(
            type='SegVFE',
            feat_channels=[64, 128, 256, 256],
            norm_cfg=vfe_norm_cfg, #SyncBN-2
            in_channels=vfe_inchannel, 
            with_voxel_center=True,
            feat_compression=16,
            return_point_feats=False), #TODO-YINING: check the return_point_feats
        backbone=dict(
            type='_Asymm3DSpconv_HM',
            inst_3d_params=inst_3d_params,
            num_inst_cls=10, # for nuscenes
            grid_size=grid_shape,
            input_channels=16,
            base_channels=32,
            more_conv=True,
            out_channels=256,
            norm_cfg=c3d_norm_cfg, #SyncBN-3
            ),
        decode_head=dict(
            type='_IALHead_MT',
            share_mt_query=True,
            save_query_update=CONSISTENCY_LOSS,
            loss_query_consistency=dict(type='mmdet.MSELoss', reduction='mean', loss_weight=0.1),
            pl_strategy='none', # only support nuscenes
            decoupled_stuff_query=decoupled_stuff_query,
            q_pe_update=q_pe_update,
            cal_PQG_recall=cal_PQG_recall,
            use_3d_pred_query=use_3d_pred_query,
            use_2d_pred_query=use_2d_pred_query,
            inst_3d_params=inst_3d_params,
            share_mpe_layer=share_mpe_layer,
            share_featfusion_layer=share_featfusion_layer,
            sem_query_feat=sem_query_feat,
            fuse_params=fuse_params,
            inst_labels=thing_class,
            num_decoder_layers=6,
            num_queries=sample_amount,
            embed_dims=256,
            cls_channels=(256, 256, num_classes), # 17 is the number of classes
            mask_channels=(256, 256, 256, 256, 256),
            thing_class=thing_class,
            stuff_class=stuff_class,
            ignore_index=ignore_index,
            num_classes=num_classes,
            point_cloud_range=point_cloud_range_polar,
            point_cloud_range_cart=point_cloud_range,
            assigner_zero_layer_cfg=dict(
                    type='mmdet.HungarianAssigner',
                    match_costs=[
                            dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                            dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                        ]),
            assigner_cfg=dict(
                    type='mmdet.HungarianAssigner',
                    match_costs=[
                            dict(type='mmdet.FocalLossCost', gamma=4.0,alpha=0.25,weight=1.0),
                            dict(type='mmdet.FocalLossCost', weight=1.0, binary_input=True, gamma=2.0, alpha=0.25),
                            dict(type='mmdet.DiceCost', weight=2.0, pred_act=True),
                        ]),
            sampler_cfg=dict(type='_MaskPseudoSampler'),
            loss_mask=dict(
                type='mmdet.FocalLoss',
                use_sigmoid=True,
                gamma=2.0,
                alpha=0.25,
                reduction='mean',
                loss_weight=1.0),
            loss_dice=dict(type='mmdet.DiceLoss', loss_weight=2.0),
            loss_cls=dict(
                type='mmdet.FocalLoss',
                use_sigmoid=True,
                gamma=4.0,
                alpha=0.25,
                loss_weight=1.0),
        ),
    ),
    # train_cfg=None,
    # test_cfg=dict(mode='whole'),
#     test_cfg=dict(
#         mode='whole',
#         num_points=100, #TODO
#         block_size=1.5,
#         sample_rate=0.5,
#         use_normalized_coord=False, #TODO
#         batch_size=1)
)
##########################################################
# optimizer and scheduler

lr = 0.0008/2
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='AdamW', lr=lr, weight_decay=0.01))
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=epoch_length*num_epoch,
        by_epoch=False,
        milestones=[int(epoch_length*num_epoch/30), int(epoch_length*num_epoch/20), int(epoch_length*num_epoch*2/30)],
        gamma=0.2)
]

# Default setting for scaling LR automatically
#   - `enable` means enable scaling LR automatically
#       or not by default.
#   - `base_batch_size` = (2 GPUs) x (16 samples per GPU).
# auto_scale_lr = dict(enable=False, base_batch_size=32)

##########################################################


##########################################################
# EVALUATION
val_evaluator = dict(
    type='_PanopticSegMetric',
    cal_PQG_recall=cal_PQG_recall,
    thing_class_inds=[1,2,3,4,5,6,7,8,10], ##MT-7: remove class truck for SG data
    stuff_class_inds=stuff_class,
    min_num_points=15,
    id_offset=seg_offset,
    dataset_type='nuscenes',
    learning_map_inv=dict({ #TODO-YINING: only for semantickitti
        0: 10,
        1: 11,
        2: 15,
        3: 18,
        4: 20,
        5: 30,
        6: 31,
        7: 32,
        8: 40,
        9: 44,
        10: 48,
        11: 49,
        12: 50,
        13: 51,
        14: 70,
        15: 71,
        16: 72,
        17: 80,
        18: 81,
        19: 0
    }))
test_evaluator = val_evaluator
##########################################################


##########################################################
# VISUALIZATION BACKEND
vis_backends = [dict(type='LocalVisBackend'),
                dict(type='WandbVisBackend',
                init_kwargs={'entity': 'pyn-sigrid-sutd-singapore-university-of-technology-design', 
                             'project': '3DDAPANO-city',
                             'name': '{{ fileBasenameNoExtension }}'})
                ]
visualizer = dict(
    type='Det3DLocalVisualizer', vis_backends=vis_backends, name='visualizer')
##########################################################


##########################################################
# HOOKS
default_hooks = dict(checkpoint=dict(type='CheckpointHook', interval=int(epoch_length/10), by_epoch=False))
custom_hooks = [
    dict(type='SavePredictionHook', 
         taskset='mini' if task_mode=='debug' else 'trainval',
         save_name='default',
         dataset_path=data_root,
         save_semantic=save_semantic,
         save_instance=save_semantic,
         save_heatmap=save_semantic, 
         save_fog=save_semantic, 
         save_panoptic=False,
         submit_panoptic=False),
]
##########################################################


##########################################################
# CUSTOM IMPORTS #TODO: Clean up
custom_imports = dict(
    imports=[
        'p3former.wrappers.ddp_meanteacher', #MT-8
        'p3former.backbones.cylinder3d_hmm',
        'p3former.backbones.swiftnet',
        'p3former.data_preprocessors.data_preprocessor_UDA_MT',
        'p3former.decode_heads.hmp3former_head',
        'p3former.decode_heads.IAL_MT_head',
        'p3former.decode_heads.heatmap_head',
        'p3former.decode_heads.heightmap_head',
        'p3former.decode_heads.soft_p3former_head',
        'p3former.segmentors.p3former_hm',
        'p3former.segmentors.IAL_MT',
        'p3former.segmentors.p3former_mm_soft',
        'p3former.task_modules.samplers.mask_pseduo_sampler',
        'evaluation.metrics.panoptic_seg_metric',
        'datasets.semidataset',
        'datasets.nuscenes_dataset_seg',
        'datasets.transforms.loading',
        'datasets.transforms.formating',
        'datasets.transforms.transforms_3d',
        'datasets.transforms.transforms_img',
        'datasets.transforms.PLRefine_add',
        'datasets.transforms.PLRefine_Grow',
        'hooks.prediction_hook',
    ],
    allow_failed_imports=False)
