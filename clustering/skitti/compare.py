import h5py
import numpy as np

def read_superpoint(filename):
    """Read superpoint

    Args:
        spt_file (_type_): _description_

    Returns:
        _type_: _description_
    """    
    data_file = h5py.File(filename, 'r')
    in_component = np.array(data_file["in_component"], dtype='uint32')
    
    return in_component

data1 = read_superpoint('/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan/08/004049.h5')
data2 = read_superpoint('/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_bak/08/004049.h5')

print(np.array_equal(data1, data2))