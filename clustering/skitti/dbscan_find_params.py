"""
Determine DBSCAN min_samples and eps based on suggestions in these links:
https://scikit-learn.org/stable/modules/clustering.html#dbscan
https://cdn.aaai.org/KDD/1996/KDD96-037.pdf
https://doi.org/10.1145/3068335

- min_samples is set based on dimension of the data, min_samples = 2 · dim. For example, with 2D data, min_samples = 4
- eps is set based on the domain knowledge or sorted k-distance plots using (2 · dim - 1) nearest neighbors
    Given min_samples, compute k nearest neighbor (k = min_samples - 1)
        k-dist function: mapping each point to the distance from its k-th nearest neighbor
    Sort the points in the data in descending order of their k-dist values
    Plot the sorted k-dist graph
    Smaller eps value is often better

Use frame 08, sequence 4049

NOTE: From the plot in dbscan_knn5_zoom.png, it seems that the eps should be between 2 and 3, so set eps = 2
Final values: min_samples = 6, eps = 2
"""
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import NearestNeighbors

def read_frame(filename: str, use_intensity: bool=False):
    """Read point cloud from file, currently accept .bin

    Args:
        filename (str): Path to LiDAR pointcloud file (only accept .bin)

    Returns:
        np.ndarray: (#points, #dim)
    """
    assert filename.endswith('.bin'), f"Trying to read {os.path.basename(filename)}. \
        File format is not accepted, only accept .bin file."

    # if all goes well, open pointcloud
    scan = np.fromfile(filename, dtype=np.float32)
    scan = scan.reshape((-1, 4))

    # put in attribute
    points = scan[:, 0:3]    # get xyz
    remissions = scan[:, 3]  # get remission

    if use_intensity:
        return scan
    else:
        return points

def main():
    pts_file = "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne/004049.bin"
    pts = read_frame(pts_file)

    n_pts, dim = pts.shape
    min_samples = dim * 2
    k_nn = min_samples - 1
    print(f"min_samples {min_samples}, k_nn {k_nn}")

    model = NearestNeighbors(n_neighbors=k_nn, metric='euclidean').fit(pts)
    distances, indices = model.kneighbors(pts)
    distance_descend = sorted(distances[:, k_nn-1], reverse=True)

    output_dir = os.path.dirname(os.path.abspath(__file__))
    plt.plot(list(range(n_pts)), distance_descend)
    plt.savefig(f"{output_dir}/dbscan_knn{k_nn}.png")
    plt.clf()

    plt.plot(list(range(n_pts)), distance_descend)
    plt.xlim(-20, 3000)
    plt.ylim(-0.1, 20)
    plt.yticks(np.arange(0, 20, 1))
    plt.savefig(f"{output_dir}/dbscan_knn{k_nn}_zoom.png")
    plt.clf()

if __name__ == "__main__":
    main()