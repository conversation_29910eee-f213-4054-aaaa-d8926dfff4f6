points_dir:         # Raw point cloud, contains folders for each sequences, e.g., 00, 01, etc.
    semantickitti: "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences"

labels_dir:         # Labels 
    semantickitti: "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences"

superpoints_dir:    # 
    semantickitti: "/home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k1000"

sequences: [8] # [8]
frames: [4049]
# frames: [4049, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 100, 200, 300]

output_dir:         # To save the point cloud with color for viewing on local
    semantickitti: "/home/<USER>/storage/code/3dmmpano/clustering/tmp/kmeans_k1000"