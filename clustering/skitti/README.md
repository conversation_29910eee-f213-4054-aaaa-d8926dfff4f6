## 2023-11-20

### General
- Output (including visualization) on 08/004049 is at [link](https://www.dropbox.com/scl/fo/h60mh4pzmgatztibgmgbj/h?rlkey=m5wsc0ijbs1nphikansb4vais&dl=0)
- Use KMeans and DBSCAN clustering
    - KMeans: With K = 10 000 clusters, it takes 3.5 mins per frames, and the code is still not finished running. So I switched to K = 1000 clusters.
    - DBSCAN: There are 2 parameters to tune: 
        - `min_samples`: Mininum number of samples to be considered as a cluster
        - `eps`: Cluster radius (local radius to expand clusters during run)
    
        I tried to follow the suggestions from Sklearn documentation and previous papers to find the appropriate parameters, the details & notes is in `clustering/dbscan_find_params.py` file (please check the comments at the beginning of the file). This leads to setting `min_samples = 6, eps = 2`. However, when evaluating on frame 4049, the performance is worse than `min_samples = 5, eps = 0.5`. I think `eps = 2` might be too large. Performances on 1 frame for these 2 sets of hyperparameters are at: [min_samples = 6, eps = 2](./output_metrics/results_logs/20231120_203210_seq8frame4049_dbscan-eps2-minsamples6/) and [min_samples = 5, eps = 0.5](./output_metrics/results_logs/20231120_203619_seq8frame4049_dbscan-eps0.5-minsamples5/).

        Additionally, the previous papers mentioned that `min_samples` might not affect the performance much. So, I set `min_samples = 5` and tried with 3 different values of `eps = 0.5, eps = 0.3, eps = 0.1`.
- Use sequence 08. For the case of 1 frame evaluation, use frame 4049; else, use all frames.
- Use the same evaluation and visualization code as when using superpoint graph.
    - Evaluation code: `spg_evaluation/spg_eval.py`
    - Visualization code: `visualization/visualize.py`

### Evaluation results
Main notes: KMeans gives better results than DBSCAN. We may want to tune the hyper-parameters more for DBSCAN if we want to use it. Adding intensity slightly improves the performance, but not much.

In each result folder:
- Evaluation by frames is in file `result_byframe.json`
- Evaluation of all frames is in file `result.json`
- All output logs of each frames and all frames is in file `result.log`

#### Without intensity
##### Use only frame 4049 (124273 points)

| method (params) | #clusters | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- | --- |
| KMeans (k 1000) | 1000 | 0.5377 | 0.6842 | [./output_metrics/results_logs/20231120_194121_seq8_kmeans1000](./output_metrics/results_logs/20231120_194121_seq8_kmeans1000/) |
| DBSCAN (min_samples 5, eps 0.1) | 623 | 0.0422 | 0.0622 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.1-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.1-minsamples5/) | 
| DBSCAN (min_samples 5, eps 0.3) | 707 | 0.2485 | 0.3525 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5/) | 
| DBSCAN (min_samples 5, eps 0.5) | 525 | 0.1616 | 0.1895 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.5-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.5-minsamples5/) | 

##### Use all frames
| method (params) | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- |
| KMeans (k 1000) | 0.7192 | 0.9024 | [./output_metrics/results_logs/20231120_194121_seq8_kmeans1000](./output_metrics/results_logs/20231120_194121_seq8_kmeans1000/) |
| DBSCAN (min_samples 5, eps 0.1) | 0.2915 | 0.4060 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.1-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.1-minsamples5/) | 
| DBSCAN (min_samples 5, eps 0.3) | 0.3664 | 0.4661 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5/) | 
| DBSCAN (min_samples 5, eps 0.5) | 0.3121 | 0.3792 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.5-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.5-minsamples5/) | 


#### With intensity
Only run for KMeans (k 1000) and DBSCAN (min_samples 5, eps 0.3)
##### Use only frame 4049 (124273 points)

| method (params) | #clusters | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- | --- |
| KMeans (k 1000) | 1000 | 0.5494 | 0.6819 | [./output_metrics/results_logs/20231120_194121_seq8_kmeans1000](./output_metrics/results_logs/20231120_194121_seq8_kmeans1000/) |
| DBSCAN (min_samples 5, eps 0.3) | 789 | 0.2525 | 0.3556 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5/) | 

##### Use all frames
| method (params) | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- |
| KMeans (k 1000) | 0.7216 | 0.9033 | [./output_metrics/results_logs/20231120_194121_seq8_kmeans1000](./output_metrics/results_logs/20231120_194121_seq8_kmeans1000/) |
| DBSCAN (min_samples 5, eps 0.3) | 0.3615 | 0.4788 | [./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5](./output_metrics/results_logs/20231120_194121_seq8_dbscan-eps0.3-minsamples5/) | 

### Clustering commands
#### KMeans 
Without intensity
```bash
sh clustering/run_cluster_kmeans1000.sh
```

With intensity
```bash
sh clustering/run_cluster_kmeans1000_intensity.sh
```

#### DBSCAN
<!-- Debug
```bash
# Clustering: eps 0.5, min_samples 5 (debug)
python clustering/clustering.py --method dbscan --eps 0.5 --min_samples 5 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering"

# Clustering: eps 2, min_samples 6 (params chose based on 08/004049) -> not good, eps too large
python clustering/clustering.py --method dbscan --eps 2 --min_samples 6 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering"

# debug.ipynb: unit probably is meter
python clustering/clustering.py --method dbscan --eps 0.5 --min_samples 5 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering"
python clustering/clustering.py --method dbscan --eps 0.3 --min_samples 5 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering"
python clustering/clustering.py --method dbscan --eps 0.1 --min_samples 5 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering"
``` -->

Without intensity
```bash
sh clustering/run_cluster_dbscan.sh
```

With intensity
```bash
sh clustering/run_cluster_dbscan_intensity.sh
```

#### Evaluation
Without intensity
```bash
sh clustering/run_eval.sh
```

With intensity
```bash
sh clustering/run_eval_intensity.sh
```

### Visualization
#### KMeans
Without intensity
```bash
python visualization/visualize.py -c clustering/visualize_configs/kmeans1000.yaml -k visualization/semantic-kitti.yaml
```

With intensity
```bash
python visualization/visualize.py -c clustering/visualize_configs/kmeans1000_intensity.yaml -k visualization/semantic-kitti.yaml
```

#### DBSCAN
Without intensity
```bash
python visualization/visualize.py -c clustering/visualize_configs/dbscan-eps0.1-mins5.yaml -k visualization/semantic-kitti.yaml
python visualization/visualize.py -c clustering/visualize_configs/dbscan-eps0.3-mins5.yaml -k visualization/semantic-kitti.yaml
python visualization/visualize.py -c clustering/visualize_configs/dbscan-eps0.5-mins5.yaml -k visualization/semantic-kitti.yaml
```

With intensity
```bash
python visualization/visualize.py -c clustering/visualize_configs/dbscan-eps0.3-mins5_intensity.yaml -k visualization/semantic-kitti.yaml
```

### Others
Get number of clusters

Without intensity
```bash
python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.1_min_samples5/08" -f 4049

python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.3_min_samples5/08" -f 4049

python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.5_min_samples5/08" -f 4049

python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k1000/08" -f 4049
```

With intensity
```bash
python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.3_min_samples5_intensity/08" -f 4049

python spg_evaluation/compute_point_reduction_rate.py -r "/home/<USER>/storage/data/SemanticKITTI/dataset/sequences/08/velodyne" -s "/home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k1000_intensity/08" -f 4049
```