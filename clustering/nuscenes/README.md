## 2023-11-25

### General
1. For nuScenes evaluation, change the followings:
    - I kept getting error message that the labels in ground truth and clustering labels do not matched. I think the problem was because of this line: https://github.com/pynsigrid/3dmmpano/blob/48f3246f639c0f31a3b3d0521f77bc00402e178d/evaluation/functional/panoptic_seg_eval.py#L296C10-L296C10
        ```python
        if self.dataset_type == 'nuscenes':
            gt_instances = gt_instances % self.id_offset + gt_semantics * self.id_offset
        ```
        Actually I don't understand why nuScenes needs this line and when I checked nuscenes-devkit, it seems that they don't include the above line (https://github.com/nutonomy/nuscenes-devkit/blob/9b165b1018a64623b65c17b64f3c9dd746040f36/python-sdk/nuscenes/eval/panoptic/panoptic_seg_evaluator.py#L86C43-L86C43). So I changed it to:
        ```python
        if self.dataset_type == 'nuscenes':  
            gt_instances = gt_instances 
        ```
    - Set `id_offset = 2 ** 32` similar to nuscenes-devkit, this is different than the value used to convert the panoptic_mask to semantic_mask. For the conversion, I still keep it as `pts_semantic_mask = pts_panoptic_mask // 1000`
    - For the min_num_points in evaluation, for semantickitti, it was set to 50 but for nuscenes I set it to 30 followings nuscenes-devkit.
2. Output (including visualization) on 1 frame (frame 27 in validation set) is at [link](https://www.dropbox.com/scl/fo/xduva725nhjnxxaed0sgl/h?rlkey=kpu47s8cfkx7iau6sh9xgrlch&dl=0)
3. Use the same evaluation and visualization code as when using superpoint graph.
    - Evaluation code: `spg_evaluation/spg_eval.py`
    - Visualization code: `visualization/visualize.py`

### Evaluation results
**MAIN NOTES:**
- For clustering: KMeans gives better results than DBSCAN. KMeans with K = 10000 is better than K = 1000. 
- For KMeans, adding intensity without normalization slightly decreases the performance. But after normalizing intensity, gives similar results as without intensity.
- Using superpoint graph features instead of x, y, z for KMeans leads to worse performance.
- KMeans clustering is slightly better than superpoint graph.
- On 1 frame, `fast_pytorch_kmeans` gives similar results, but slower. For K = 10000, sklearn KMeans takes 0.20 mins, `fast_pytorch_kmeans` takes 0.94 mins.

In each result folder:
- Evaluation by frames is in file `result_byframe.json`
- Evaluation of all frames is in file `result.json`
- All output logs of each frames and all frames is in file `result.log`

#### Clustering
##### On 1 frame
Use frame 27 in `nuscenes_infos_val.pkl`, filename = `n008-2018-08-01-15-16-36-0400__LIDAR_TOP__1533151616947490.pcd.bin`
- For KMeans, try with K = 1000, 10000
- For DBSCAN, try with min_samples = 4, 5, 6; eps = 0.1, 0.3, 0.5. Only include best result in the table below, all results are at [../output_metrics/nuscenes/results_logs_dbscan/](../output_metrics/nuscenes/results_logs_dbscan/)

**=> Conclusion: Use KMeans with K = 10000 for all frames**

| method (params) | #clusters | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- | --- |
| KMeans (k 1000) | 1000 | 0.4182 | 0.4862 | [../output_metrics/nuscenes/results_logs/20231126_023252_frame27_kmeans1000](../output_metrics/nuscenes/results_logs/20231126_023252_frame27_kmeans1000/) |
| KMeans (k 10000) | 10000 | 0.7346 | 0.75 | [../output_metrics/nuscenes/results_logs/20231126_023252_frame27_kmeans10000](../output_metrics/nuscenes/results_logs/20231126_023252_frame27_kmeans10000/) |
| KMeans (k 10000 with `fast_pytorch_kmeans`) | 10000 | 0.7343 | 0.75 | [../output_metrics/nuscenes/results_logs/20231127_094358_frame27_kmeans10000_fast](../output_metrics/nuscenes/results_logs/20231127_094358_frame27_kmeans10000_fast/) |
| DBSCAN (min_samples 4, eps 0.5) |  | 0.1542 | 0.2240 | [../output_metrics/nuscenes/results_logs_dbscan/20231126_155155_dbscan-eps0.5-minsamples4](../output_metrics/nuscenes/results_logs_dbscan/20231126_155155_dbscan-eps0.5-minsamples4/) | 

##### On all frames
Use KMeans with K = 10000, with or without intensity. For row with `with normalize`, the intensity value is normalized to [0, 1] by dividing to 255.

| method (params) | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- |
| KMeans, without intensity | 0.7952 | 0.8125 | [../output_metrics/nuscenes/results_logs/20231126_030133_kmeans10000](../output_metrics/nuscenes/results_logs/20231126_030133_kmeans10000/) |
| KMeans, with intensity | 0.7783 |  0.8123 | [../output_metrics/nuscenes/results_logs/20231126_160548_kmeans10000_intensity](../output_metrics/nuscenes/results_logs/20231126_160548_kmeans10000_intensity/) |
| KMeans, with intensity, with normalize | 0.7957 | 0.8125 | [../../clustering/output_metrics/nuscenes/results_logs/20231127_101807_kmeans10000_intensity_norm](../../clustering/output_metrics/nuscenes/results_logs/20231127_101807_kmeans10000_intensity_norm/) |

#### Superpoint graph
1. Try on 1 frame: frame 27 in `nuscenes_infos_val.pkl`, filename = `n008-2018-08-01-15-16-36-0400__LIDAR_TOP__1533151616947490.pcd.bin` with the following hyperparameters:
    - reg_strength: 0.001, 0.01, 0.1
    - lambda_edge_weight: 0.1, 1, 10
    - k_nn_adj: 5, 10, 15, 20
    - k_nn_geof: 10, 20, 30, 40, 50 (Note: only include k_nn_geof such that k_nn_adj <= k_nn_geof)

    Summary results is at [3dmmpano/spg_evaluation/output_metrics/nuscenes_tune/summary_sortbypq.csv](../../spg_evaluation/output_metrics/nuscenes_tune/summary_sortbypq.csv). Best hyperparameter set: reg_strength = 0.001, lambda_edge_weight = 10, k_nn_adj = 5, k_nn_geof = 50
2. Run the best hyperparameter set on all frames, with and without intensity. With intensity, intensity is normalized.

| method (params) | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- |
| Without intensity | 0.7837 | 0.8125 | [../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_014650_reg0.001_lambda10_kadj5_kgeof50](../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_014650_reg0.001_lambda10_kadj5_kgeof50/) |
| With intensity | 0.7760 | 0.8125 | [../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_014650_reg0.001_lambda10_kadj5_kgeof50_intensity](../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_014650_reg0.001_lambda10_kadj5_kgeof50_intensity/) |

#### Clustering with superpoint graph's features
Use KMeans with superpoint graph features on all frames, with and without intensity. With intensity, intensity is normalized.
For superpoint graph part, the hyperparamters are: `reg_strength = 0.001, lambda_edge_weight = 10, k_nn_adj = 5, k_nn_geof = 50`

| method (params) | pq (all classes) | rq (all classes) | output folder | 
| --- | --- | --- | --- |
| Without intensity | 0.3161 | 0.4818 | [../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_091911_reg0.001_lambda10_kadj5_kgeof50_kmeans10000](../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_091911_reg0.001_lambda10_kadj5_kgeof50_kmeans10000/) |
| With intensity | 0.5251 | 0.7322 | [../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_091911_reg0.001_lambda10_kadj5_kgeof50_kmeans10000_intensity](../../spg_evaluation/output_metrics/nuscenes/results_logs/20231127_091911_reg0.001_lambda10_kadj5_kgeof50_kmeans10000_intensity/) |

### Commands
#### Clustering
On 1 frame
```bash
# KMeans
sh clustering/scripts/nuscenes/run_cluster_kmeans_debug.sh
sh clustering/scripts/nuscenes/run_eval_kmeans_debug.sh

# KMeans Pytorch
sh clustering/scripts/nuscenes/run_cluster_kmeans_debug_fastkmeans.sh
sh clustering/scripts/nuscenes/run_eval_kmeans_debug_fastkmeans.sh

# DBSCAN
sh clustering/scripts/nuscenes/run_cluster_dbscan.sh
sh clustering/scripts/nuscenes/run_eval_dbscan.sh
```

On all frames (KMeans 10k)
```bash
# Without intensity
sh clustering/scripts/nuscenes/run_cluster_kmeans10000.sh
sh clustering/scripts/nuscenes/run_eval_kmeans.sh

# With intensity 
sh clustering/scripts/nuscenes/run_cluster_kmeans10000_intensity.sh
sh clustering/scripts/nuscenes/run_eval_kmeans_intensity.sh

# With intensity, with normalization
sh clustering/scripts/nuscenes/run_cluster_kmeans10000_intensity_norm.sh
sh clustering/scripts/nuscenes/run_eval_kmeans_intensity_norm.sh
```
#### Superpoint graph
```bash
1. Tune hyperparams
# In superpoint_graph folder
bash 1-partition_nuscenes_tune.sh

2. Evaluate 
# In 3dmmpano folder
bash spg_evaluation/nuscenes/run_tune.sh
```
#### Clustering with superpoint graph's features
```bash
# In superpoint_graph folder
bash 1-partition_nuscenes_cluster.sh

# In 3dmmpano folder
bash spg_evaluation/nuscenes/run_cluster.sh
```

### Visualization
#### Visualize using saved files
Use command:
```bash
python visualization/nuscenes/visualize.py -m f -p "<path_to_file>/gt_sem.ply" "<path_to_file>/spt_sem.ply"
```
<!-- 
```bash
python visualization/nuscenes/visualize.py -m f -p "/home/<USER>/Dropbox/Work/SideProjects/3dmmpano/Share/20231125_231820/Samples_visualize/nuscenes_frame27validation_kmeans1000/20231127_100739/n008-2018-08-01-15-16-36-0400__LIDAR_TOP__1533151616947490.pcd/gt_sem.ply" "/home/<USER>/Dropbox/Work/SideProjects/3dmmpano/Share/20231125_231820/Samples_visualize/nuscenes_frame27validation_kmeans1000/20231127_100739/n008-2018-08-01-15-16-36-0400__LIDAR_TOP__1533151616947490.pcd/spt_sem.ply"
``` -->

#### KMeans
Without intensity
```bash
python visualization/nuscenes/visualize.py -c clustering/nuscenes/visualize_configs/kmeans1000.yaml -k visualization/nuscenes/nuscenes.yaml
python visualization/nuscenes/visualize.py -c clustering/nuscenes/visualize_configs/kmeans10000.yaml -k visualization/nuscenes/nuscenes.yaml
```

With intensity
```bash
python visualization/nuscenes/visualize.py -c clustering/nuscenes/visualize_configs/kmeans1000_intensity.yaml -k visualization/nuscenes/nuscenes.yaml
python visualization/nuscenes/visualize.py -c clustering/nuscenes/visualize_configs/kmeans10000_intensity.yaml -k visualization/nuscenes/nuscenes.yaml
```

#### Superpoint graph
```bash
python visualization/nuscenes/visualize.py -c spg_evaluation/nuscenes/visualize_configs/spg_reg0.001_lambda10_kadj5_kgeof50.yaml -k visualization/nuscenes/nuscenes.yaml
```