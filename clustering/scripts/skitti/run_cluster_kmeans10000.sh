
log_dir='./clustering/logs'
mkdir -p $log_dir
date=`date +"%Y%m%d_%H%M%S"`
log_file="${log_dir}/${date}_clustering_kmeans10000.txt"

# python clustering/clustering.py --method kmeans --n_clusters 10000 --ncpus 20 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" | tee -a "$log_file"
python clustering/clustering.py --method kmeans --n_clusters 10000 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" | tee -a "$log_file"