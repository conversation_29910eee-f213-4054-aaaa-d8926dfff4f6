
log_dir='./clustering/logs'
mkdir -p $log_dir

# # debug
# date=`date +"%Y%m%d_%H%M%S"`
# log_file1="${log_dir}/${date}_clustering_dbscan-eps2-minsamples6.txt"
# python clustering/clustering.py --method dbscan --eps 2 --min_samples 6 --frames 4049 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" >> "$log_file1" 
# sleep 30

date=`date +"%Y%m%d_%H%M%S"`
log_file1="${log_dir}/${date}_clustering.txt"
python clustering/clustering.py --method dbscan --eps 0.5 --min_samples 5 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" >> "$log_file1" & 

sleep 30

date=`date +"%Y%m%d_%H%M%S"`
log_file2="${log_dir}/${date}_clustering.txt"
python clustering/clustering.py --method dbscan --eps 0.3 --min_samples 5 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" >> "$log_file2" & 

sleep 30

date=`date +"%Y%m%d_%H%M%S"`
log_file3="${log_dir}/${date}_clustering.txt"
python clustering/clustering.py --method dbscan --eps 0.1 --min_samples 5 --output_dir "/home/<USER>/storage/data/SemanticKITTI_clustering" >> "$log_file3" & 

wait
echo "All done"