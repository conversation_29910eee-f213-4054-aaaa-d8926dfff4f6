date=`date +"%Y%m%d_%H%M%S"`

sequence=8
# frame=4049

################################################################
# KMeans
echo "Evaluating KMeans k = 1000"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_kmeans1000"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix /home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k1000 --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

# ################################################################
# # DBSCAN eps 0.5 min_samples 5
# sequence=8
# log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_dbscan-eps0.5-minsamples5"

# out_dir="$log_dir"
# mkdir -p $out_dir
# python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.5_min_samples5" --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

################################################################
# DBSCAN eps 0.3 min_samples 5
echo "Evaluating DBSCAN eps = 0.3 min_samples = 5"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_dbscan-eps0.3-minsamples5"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.3_min_samples5" --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

################################################################
# DBSCAN eps 0.1 min_samples 5
echo "Evaluating DBSCAN eps = 0.1 min_samples = 5"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_dbscan-eps0.1-minsamples5"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.1_min_samples5" --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"