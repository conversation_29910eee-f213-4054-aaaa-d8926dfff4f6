date=`date +"%Y%m%d_%H%M%S"`

sequence=8
# frame=4049

################################################################
# KMeans
echo "Evaluating KMeans k = 1000"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_kmeans1000_intensity"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix /home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k1000_intensity --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

################################################################
# DBSCAN eps 0.3 min_samples 5
echo "Evaluating DBSCAN eps = 0.3 min_samples = 5"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_dbscan-eps0.3-minsamples5_intensity"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.3_min_samples5_intensity" --sequence ${sequence} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"