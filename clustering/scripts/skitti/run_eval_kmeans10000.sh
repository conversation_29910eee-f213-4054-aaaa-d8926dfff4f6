date=`date +"%Y%m%d_%H%M%S"`

sequence=8
frame=4049

################################################################
# KMeans
echo "Evaluating KMeans k = 10000"
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}_kmeans10000"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix /home/<USER>/storage/data/SemanticKITTI_clustering/kmeans_k10000 --sequence ${sequence} --frame ${frame} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"