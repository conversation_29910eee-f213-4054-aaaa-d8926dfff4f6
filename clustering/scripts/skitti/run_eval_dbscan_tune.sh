date=`date +"%Y%m%d_%H%M%S"`

sequence=8
frame=4049

################################################################
# DBSCAN eps 2 min_samples 6
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}frame4049_dbscan-eps2-minsamples6"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps2_min_samples6" --sequence ${sequence} --frames ${frame} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

################################################################
# DBSCAN eps 0.5 min_samples 5
log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/results_logs/${date}_seq${sequence}frame4049_dbscan-eps0.5-minsamples5"

out_dir="$log_dir"
mkdir -p $out_dir
python spg_evaluation/spg_eval.py --data_root /home/<USER>/storage/data/SemanticKITTI/dataset/ --spg_prefix "/home/<USER>/storage/data/SemanticKITTI_clustering/dbscan_eps0.5_min_samples5" --sequence ${sequence} --frames ${frame} --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"