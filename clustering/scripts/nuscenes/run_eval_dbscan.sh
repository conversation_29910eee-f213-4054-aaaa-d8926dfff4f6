date=`date +"%Y%m%d_%H%M%S"`

for min_samples in 4 5 6; do
    for eps in 0.1 0.3 0.5; do 
        echo "Evaluate DBSCAN clustering with min_samples ${min_samples}, eps ${eps}"

        log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/nuscenes/results_logs_dbscan/${date}_dbscan-eps${eps}-minsamples${min_samples}"

        out_dir="$log_dir"
        mkdir -p $out_dir
        python spg_evaluation/spg_eval.py --dataset nuscenes --nuscenes_ver "v1.0-mini" --nuscenes_pkl /home/<USER>/storage/data/nuscenes/nuscenes_infos_val.pkl --data_root /home/<USER>/storage/data/nuscenes/ --spg_prefix "/home/<USER>/storage/data/nuscenes_clustering/dbscan_eps${eps}_min_samples${min_samples}" --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"

        sleep 3
    done
done
echo "All done"