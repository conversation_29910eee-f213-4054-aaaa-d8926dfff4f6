
log_dir='./clustering/logs/nuscenes'
mkdir -p $log_dir
date=`date +"%Y%m%d_%H%M%S"`
log_file="${log_dir}/${date}_clustering_kmeans10000.txt"

python clustering/clustering.py --dataset nuscenes --nuscenes_ver "v1.0-mini" --nuscenes_pkl /home/<USER>/storage/data/nuscenes/nuscenes_infos_val.pkl --method kmeans --n_clusters 10000 --data_dir "/home/<USER>/storage/data/nuscenes" --output_dir "/home/<USER>/storage/data/nuscenes_clustering/" | tee -a "$log_file"
# sh clustering/scripts/nuscenes/run_cluster_kmeans10000.sh