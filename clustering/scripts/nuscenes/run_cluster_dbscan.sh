
log_dir='./clustering/logs/nuscenes'
mkdir -p $log_dir

# # debug
# date=`date +"%Y%m%d_%H%M%S"`
# log_file1="${log_dir}/${date}_clustering_dbscan-eps2-minsamples6.txt"
# python clustering/clustering.py --method dbscan --eps 2 --min_samples 6 --frames 4049 --output_dir "/home/<USER>/storage/data/nuscenes_clustering" >> "$log_file1" 
# sleep 30

for min_samples in 4 5 6; do
    for eps in 0.1 0.3 0.5; do 
        echo "DBSCAN clustering with min_samples ${min_samples}, eps ${eps}"

        date=`date +"%Y%m%d_%H%M%S"`
        log_file="${log_dir}/${date}_clustering_dbscan-eps${eps}-mins${min_samples}.txt"
        python clustering/clustering.py --dataset nuscenes --nuscenes_ver "v1.0-mini" --nuscenes_pkl /home/<USER>/storage/data/nuscenes/nuscenes_infos_val.pkl --method dbscan --eps ${eps} --min_samples ${min_samples} --data_dir "/home/<USER>/storage/data/nuscenes" --output_dir "/home/<USER>/storage/data/nuscenes_clustering/" >> "$log_file" &

        sleep 3
    done
done

wait
echo "All done"