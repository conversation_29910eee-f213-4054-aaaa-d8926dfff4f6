date=`date +"%Y%m%d_%H%M%S"`

################################################################
# KMeans with intensity without norm
for cluster in 10000; do
    echo "Evaluate KMeans clustering with k ${cluster}"
    log_dir="/home/<USER>/storage/code/3dmmpano/clustering/output_metrics/nuscenes/results_logs/${date}_kmeans${cluster}_intensity_norm"

    out_dir="$log_dir"
    mkdir -p $out_dir
    python spg_evaluation/spg_eval.py --dataset nuscenes --nuscenes_ver "v1.0-mini" --nuscenes_pkl /home/<USER>/storage/data/nuscenes/nuscenes_infos_val.pkl --data_root /home/<USER>/storage/data/nuscenes/ --spg_prefix /home/<USER>/storage/data/nuscenes_clustering/kmeans_k${cluster}_intensity_norm --output_json "${out_dir}/result.json" --eval_frame | tee -a "${out_dir}/result.log"
done 