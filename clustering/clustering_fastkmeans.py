"""Clustering using KMeans and DBSCAN
Output should be the same format as superpoint output in order to use the same evaluation code as superpoint.

Use https://github.com/DeMoriarty/fast_pytorch_kmeans for KMeans instead of sklearn
"""
import os
import os.path as osp
import numpy as np
import argparse
import h5py
import time
import copy
import sys
from tools.fast_pytorch_kmeans.fast_pytorch_kmeans.kmeans import KMeans as KMeansPytorch
from scipy.spatial.distance import cdist
import multiprocessing
from nuscenes import NuScenes
import pickle
import random
import torch

def set_random_seeds(seed):
    """
    Set all the random seeds to a fixed value for reproducibility.

    Args:
        seed (int): the seed value
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True

def read_frame(dataset, filename: str, use_intensity: bool=False, norm_intensity: bool=False):
    """Read point cloud from file, currently accept .bin

    Args:
        dataset (str): Dataset type
        filename (str): Path to LiDAR pointcloud file (only accept .bin)
        use_intensity (bool, optional): Whether to include intensity. Defaults to False.

    Returns:
        np.ndarray: (#points, #dim)
    """
    assert filename.endswith('.bin'), f"Trying to read {os.path.basename(filename)}. \
        File format is not accepted, only accept .bin file."

    if dataset == 'semantickitti':
        num_pts_feat = 4

        # if all goes well, open pointcloud
        scan = np.fromfile(filename, dtype=np.float32)
        scan = scan.reshape((-1, num_pts_feat))  # num_pts_feat = 4

        # put in attribute
        points = scan[:, 0:3]    # get xyz
        remissions = scan[:, 3]  # get remission

        if use_intensity:
            return scan
        else:
            return points
    
    elif dataset == 'nuscenes':
        num_pts_feat = 5

        scan = np.fromfile(filename, dtype=np.float32)
        scan = scan.reshape(-1, num_pts_feat)

        if use_intensity:
            points = scan[:, 0:4]
            
            # Normalize: for nuScenes, range is [0, 255]
            if norm_intensity:
                points[:, 3] /= 255
        else:
            points = scan[:, 0:3]    
        
        return points

    else:
        raise ValueError(f"dataset can only be 'semantickitti' or 'nuscenes', not {dataset}")
    
def write_clusters(filename, in_component):
    """Save clustering output in the same format as superpoint"""
    if osp.isfile(filename):
        os.remove(filename)

    with h5py.File(filename, 'w') as data_file:
        data_file.create_dataset('in_component', data=in_component, dtype='uint32')

def dbscan_assign_noise(pts, in_component):
    """DBSCAN clustering: assign noise points to the closest cluster"""  
    # Find centroid
    centroids = np.array([np.mean(pts[in_component==i], axis=0) for i in np.unique(in_component) if i != -1])

    # Assign noise points to the closest centroid
    res_array = copy.deepcopy(in_component)
    noise_pts_x, _ = np.where(in_component.reshape(-1, 1) == -1)
    for pt_x in noise_pts_x:
        closet_label = np.argmin(cdist(pts[[pt_x], :], centroids, 'euclidean'))
        res_array[pt_x] = closet_label
    return res_array

def process_frame(frame):
    frame_stime = time.time()

    if dataset == 'semantickitti':
        pts_file = osp.join(sequence_indir, f"{str(frame).zfill(6)}.bin")
        cluster_file = osp.join(sequence_outdir, f"{str(frame).zfill(6)}.h5")
    elif dataset == 'nuscenes':
        pts_file = osp.join(sequence_indir, f"{frame}.bin")
        cluster_file = osp.join(sequence_outdir, f"{frame}.h5")
    if not overwrite and osp.isfile(cluster_file):
        print(f"Frame {frame}: Cluster output exist, skipping")
        return

    pts = read_frame(dataset, pts_file, intensity, norm_intensity)
    print(f"Processing frame {frame}: {pts.shape}")

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    pts = torch.from_numpy(pts).float().to(device)

    model = KMeansPytorch(n_clusters=int(n_clusters), init_method='kmeans++')
    cluster_labels = model.fit_predict(pts)
    cluster_labels = cluster_labels.detach().cpu().numpy()
    print(f"--- Got {len(np.unique(cluster_labels))} clusters")
    write_clusters(cluster_file, cluster_labels)

    # For DBSCAN
    if method == 'dbscan':
        # Number of clusters in labels, ignoring noise if present.
        n_clusters_ = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        n_noise_ = list(cluster_labels).count(-1)
        print(f"--- # clusters: {n_clusters_}, # noise points: {n_noise_}")

        # Assign noise points to the closest cluster
        dbscan_assign_noise(pts, cluster_labels)
    sys.stdout.flush()

    frame_etime = time.time()
    print(f"--- Process frame finished in {(frame_etime - frame_stime)/60} mins")

def process_frame_multiprocess(frames_list, ncpus=1):
    print(f"INFO: Using {ncpus} cpus")
    if ncpus > 1:
        with multiprocessing.Pool(processes=ncpus, maxtasksperchild=3) as p:
            p.map(process_frame, frames_list, chunksize=3)
    else:
        for frame in frames_list:
            process_frame(frame)

def get_args():
    """
    For KMeans, need to pass in num_clusters
    For DBSCAN, need to pass in min_samples and eps
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--method', help="Clustering method, kmeans or dbscan", choices=['kmeans', 'dbscan'], required=True)
    parser.add_argument('--dataset', help="Dataset name", choices=['semantickitti', 'nuscenes'], default='semantickitti')
    parser.add_argument('--nuscenes_pkl', help="NuScenes pkl file", default=None)
    parser.add_argument('--data_dir', help="Data folder", default="/home/<USER>/storage/data/SemanticKITTI/dataset")
    parser.add_argument('--output_dir', help="Output folder to save cluster", required=True, default="/home/<USER>/storage/data/SemanticKITTI_clustering")
    parser.add_argument('--sequence', help="Sequence ID", default='08')
    parser.add_argument('--frames', nargs='+', help="Frame ID list, if empty, use all frames", default=None, type=int)
    parser.add_argument('--frame_start', help="Frame index at the begining", default=None, type=int)
    parser.add_argument('--frame_end', help="Frame index at the end", default=None, type=int)
    parser.add_argument('--ncpus', help="Number of CPUs to use", default=1, type=int)
    parser.add_argument('--overwrite', help="If passed, overwrite old results", action="store_true", default=False)
    parser.add_argument('--intensity', help="If passed, use intensity", action="store_true", default=False)
    parser.add_argument('--norm_intensity', action='store_true', help='For nuScenes, normalize if this value is True')

    parser.add_argument('--nuscenes_ver', help="nuScenes version", default=None)
    parsed, unknown = parser.parse_known_args()
    
    # unknown is used to pass in the hyperparameters of the clustering method
    for arg in unknown:
        if arg.startswith(("-", "--")):
            parser.add_argument(arg.split('=')[0])
    args = parser.parse_args()

    if args.method == 'kmeans':
        if 'n_clusters' not in vars(args):
            parser.error('KMeans requires --n_clusters argument')

    return args

def read_pickle(file_name):
    with open(file_name, 'rb') as f:
        _data = pickle.load(f)
    return _data

def main(args):
    """Main clustering function"""
    RANDOM_SEED = 1234
    BASE_INDIR = args.data_dir
    BASE_OUTDIR = args.output_dir

    global dataset, method, overwrite, sequence_indir, sequence_outdir, model, intensity, norm_intensity, n_clusters
    dataset = args.dataset
    method = args.method
    overwrite = args.overwrite
    if dataset == 'semantickitti':
        sequence = str(args.sequence).zfill(2)
        sequence_indir = osp.join(BASE_INDIR, 'sequences', sequence, 'velodyne')
    elif dataset == 'nuscenes':
        sequence = ''
        sequence_indir = osp.join(BASE_INDIR, 'samples', 'LIDAR_TOP')
    intensity = args.intensity
    norm_intensity = args.norm_intensity
    intensity_suffix = "_intensity" if intensity else ""
    intensity_suffix += "_norm" if norm_intensity else ""
    print('INFO: Intensity', intensity)

    # Check input
    if args.dataset == 'nuscenes':
        if args.nuscenes_pkl is None:
            raise ValueError("Missing nuscenes_pkl for nuScenes dataset")
        if args.nuscenes_ver is None:
            raise ValueError("Missing nuscenes_ver for nuScenes dataset")

    # Prepare data
    if args.frames is None:
        if dataset == 'semantickitti':
            frames_list = sorted([int(i.split('.bin')[0]) for i in os.listdir(sequence_indir)])
        elif dataset == 'nuscenes':
            _data = read_pickle(args.nuscenes_pkl)
            frames_list = [i['lidar_points']['lidar_path'].split('.bin')[0] for i in _data['data_list']]
    else:
        if dataset == 'semantickitti':
            frames_list = args.frames
        elif dataset == 'nuscenes':
            _data = read_pickle(args.nuscenes_pkl)['data_list']

            frames_list = []
            for item in args.frames:
                item_path = _data[item]['lidar_points']['lidar_path'].split('.bin')[0]
                frames_list.append(item_path)

    if args.frame_start is not None and args.frame_end is not None:
        args.frame_end = len(frames_list) if args.frame_end > len(frames_list) else args.frame_end
        frames_list = frames_list[args.frame_start:args.frame_end]
    print(f"INFO: Number of frames {len(frames_list)}")

    # Create clustering model
    set_random_seeds(RANDOM_SEED)
    if args.method == 'kmeans':
        print(f"INFO: Clustering with KMeans, k = {args.n_clusters}")
        n_clusters = args.n_clusters
        sequence_outdir = osp.join(BASE_OUTDIR, f"{args.method}_k{args.n_clusters}{intensity_suffix}_fastkmeans", sequence)

    # elif args.method == 'dbscan':
    #     print(f"INFO: Clustering with DBSCAN, eps = {args.eps}, min_samples = {args.min_samples}")
    #     model = DBSCAN(eps=float(args.eps), min_samples=int(args.min_samples))
    #     sequence_outdir = osp.join(BASE_OUTDIR, f"{args.method}_eps{args.eps}_min_samples{args.min_samples}{intensity_suffix}", sequence)
    
    else:
        raise NotImplementedError
    print(f"INFO: Output {sequence_outdir}")
    os.makedirs(sequence_outdir, exist_ok=True)
    
    # Main work
    start_time = time.time()
    if method == 'kmeans':
        process_frame_multiprocess(frames_list, ncpus=args.ncpus)
    
    elif method == 'dbscan':
        process_frame_multiprocess(frames_list, ncpus=args.ncpus)

    else:
        raise NotImplementedError
    
    print(f"Elapsed time: {(time.time() - start_time)/60} mins")

if __name__ == "__main__":
    dataset = ''
    method = None
    overwrite = None
    sequence_indir = ''
    sequence_outdir = ''
    model = None
    intensity = False
    norm_intensity = False

    # Kmeans
    n_clusters = None

    main(get_args())