# LOG_FILE='work_dirs_new/p3former_1xb2_nuscenes_trainval_single_aug_r50_lr8e-4_rflc/vis_data/20240207_103348.json'
# LOG_FILE2='work_dirs_new/nuscenes_trainval_soft_2Dquery_V3/20240626_193225/vis_data/20240626_193225.json'
LOG_FILE='work_dirs/IAL_MT_city_syncBuffer_PLgrowSGth_XshareQ_Xconsist_HPC4/20250721_085856/vis_data/20250721_085856.json'
LOG_FILE2='work_dirs/IAL_MT_city_syncBuffer_PLgrowSGst_XshareQ_Xconsist_HPC3/20250721_021001/vis_data/20250721_021001.json'
SAVE_PATH='output/plot_curve_UDA'
# SAVE_NAME="Compare_2D_inst_query"
SAVE_NAME="grow_th_st"
SAVE_FILE="$SAVE_PATH/$SAVE_NAME"

############################################################################################################
# plot overall results by epoch
# plot two loss
echo "*** Plotting loss curve... ***"
python tools/analysis_tools/analyze_logs.py \
    plot_curve \
    $LOG_FILE $LOG_FILE2 \
    --keys "loss" \
    --title "loss" \
    --out "$SAVE_FILE _loss.png" \
    --legend "exp#9_w/o_add" "exp#9_w/_add" \
    # --eval \

# plot two pq
echo "*** Plotting pq curve... ***"
python tools/analysis_tools/analyze_logs.py \
    plot_curve \
    $LOG_FILE $LOG_FILE2 \
    --keys "pq" \
    --title "pq" \
    --out "$SAVE_FILE _pq.png" \
    --legend "exp#9_w/o_add" "exp#9_w/_add" \
    # --eval \

# pplot miou
echo "*** Plotting pq curve... ***"
python tools/analysis_tools/analyze_logs.py \
    plot_curve \
    $LOG_FILE $LOG_FILE2 \
    --keys "miou" \
    --title "miou" \
    --out "$SAVE_FILE _miou.png" \
    --legend "exp#9_w/o_add" "exp#9_w/_add" \
    # --eval \

# ############################################################################################################
# # plot class wise results by epoch
# LOG_FILE2_detail='work_dirs_new/nuscenes_trainval_mm_CPEv2_hm_IDS/20240703_070524.log'
# LOG_FILE_detail='work_dirs_new/nuscenes_trainval_soft_CPEv2_HPC4/20240429_044117/20240429_044117.log'

# python tools/analysis_tools/analyze_log_class_wise.py \
#     --log_file1 $LOG_FILE_detail \
#     --log_file2 $LOG_FILE2_detail \
#     --log_name1 "w/o aux loss" \
#     --log_name2 "w/ aux loss" \
#     --save_path $SAVE_PATH/class_wise \
#     --subfolder_name $SAVE_NAME 


# LOG_FILE4='work_dirs_new/3DQ/nuscenes_trainval_single_hm_sigma3_bin+height_lrHM01_HPC5/20240724_185934/vis_data/scalars.json'
# LOG_FILE3='work_dirs_new/3DQ/nuscenes_trainval_single_hm_sigma5_bin_lrHM01_HPC4/20240730_045924/vis_data/20240730_045924.json'
# LOG_FILE5='work_dirs_new/3DQ/nuscenes_trainval_single_hm_sigma3_bin+height_lrHM_HPC5/20240723_141113/vis_data/20240723_141113.json'
# # plot heatmap of loss
# echo "*** Plotting loss curve... ***"
# python tools/analysis_tools/analyze_logs.py \
#     plot_curve \
#     $LOG_FILE3 $LOG_FILE4 $LOG_FILE5 \
#     --keys "decode.loss_heatmap" \
#     --title "loss_heatmap" \
#     --out "$SAVE_FILE _loss_heatmap.png" \
#     --legend "MSE,s=5,ago,lr=8e-5" "MSE,s=3,ago,lr=8e-5" "MSE,s=3,ago,lr=8e-4"\
#     # --eval \

# # plot fog loss
# LOG_FILE6='work_dirs_new/nuscenes_trainval_mm_hms_fogs_CPEv2_all/20240827_050300/vis_data/20240827_050300.json'
# echo "*** Plotting FOG loss curve... ***"
# SAVE_FILE="$SAVE_PATH _loss_fog"
# python tools/analysis_tools/analyze_logs.py \
#     plot_curve \
#     $LOG_FILE6 \
#     --keys "decode.loss_fog" \
#     --title "loss_fog" \
#     --out "$SAVE_FILE _loss_fog.png" \
#     --legend "fog" \
#     # --eval \