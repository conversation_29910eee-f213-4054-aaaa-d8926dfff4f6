import numpy as np
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS
from tools.refine_pseudo_labels import refine_pseudo_labels

# only support nuscenes
@TRANSFORMS.register_module(force=True)
class PLRefine_template(BaseTransform):
    """根据GT的semantic和instance mask来grow pseudo label。
    
    Args:
        ignore_label (int): 忽略的标签值，默认为-100。
    """
    def __init__(self, ignore_label: int = 0):
        self.ignore_label = ignore_label

    def transform(self, input_dict):
        """
        return original input_dict
        """

        return input_dict 

@TRANSFORMS.register_module(force=True)
class PLRefine_filterv1(BaseTransform):
    """
    Filter high semantic confidence points for both semantic and instance mask. (PLv1)
    """
    def __init__(self, ignore_label: int = 0):
        self.ignore_label = ignore_label

    def transform(self, input_dict):
        """
        Args:
            input_dict (dict):
                - pseudo_label.pts_semantic_mask: semantic mask
                - pseudo_label.pts_sem_score: semantic score
                - pseudo_label.pts_inst_score: instance score

        Returns:
            dict: 处理后的输入字典，包含grown的语义和实例标签。 (PLv2)
        """
        pl_sem = input_dict['pseudo_label']['pts_semantic_mask']
        pl_ins = input_dict['pseudo_label']['pts_instance_mask']
        pl_sem_score = input_dict['pseudo_label']['pts_sem_score']

        filter_sem = refine_pseudo_labels(pl_sem_score, pl_sem, ignore_label=self.ignore_label) 
        filter_ins = refine_pseudo_labels(pl_sem_score, pl_ins, ignore_label=self.ignore_label)

        # 更新pseudo_label中的标签
        input_dict['pseudo_label']['pts_semantic_mask'] = filter_sem
        input_dict['pseudo_label']['pts_instance_mask'] = filter_ins

        return input_dict 