import numpy as np
from typing import List, Optional, Sequence
from mmcv.transforms import BaseTransform
from mmdet3d.registry import TRANSFORMS

@TRANSFORMS.register_module(force=True)
class PLDrop(BaseTransform):
    """
    Drop low-confidence pseudo-label instances (no dedup).
    规则：
      1) 若实例的主导(majority)语义在 stuff_classes 中 => 直接整块保留；
      2) 否则，若 inst_score < drop_thr 且 inst_score < 类平均分 => 整块删除；
      3) ignore/stuff 类是否允许删除由开关控制（默认不删）。
    """
    def __init__(self,
                 drop_thr: float = 0.30,
                 stuff_classes: Optional[List[int]] = None,
                 ignore_classes: Optional[List[int]] = None,
                 drop_stuff: bool = False,     # 默认不丢弃 stuff
                 drop_ignore: bool = False,    # 默认不丢弃 ignore
                 min_pts_per_inst: int = 1):
        self.drop_thr = float(drop_thr)
        self.stuff_classes = stuff_classes
        self.ignore_classes = ignore_classes
        if isinstance(ignore_classes, int):
            self.ignore_classes = [ignore_classes]
        self.drop_stuff = bool(drop_stuff)
        self.drop_ignore = bool(drop_ignore)
        self.min_pts_per_inst = int(min_pts_per_inst)

    def transform(self, input_dict):
        # 逐点字段
        points     = input_dict['points']                                # (N,C)
        pl_psem    = input_dict['pseudo_label']['pts_semantic_mask']     # (N,)
        pl_pins    = input_dict['pseudo_label']['pts_instance_mask']     # (N,)
        pl_pscore  = input_dict['pseudo_label']['pts_sem_score']         # (N,)
        sam_pmask  = input_dict['sam']['sam_pmask']                      # (N,)
        sam_pscore = input_dict['sam']['sam_pscore']                     # (N,)
        aug_mask   = input_dict.get('aug_mask', None)                    # (N,) or None

        assert isinstance(points, np.ndarray), "Points should be a numpy array."
        N = points.shape[0]
        assert N == pl_psem.shape[0] == pl_pins.shape[0] == pl_pscore.shape[0] == sam_pmask.shape[0] == sam_pscore.shape[0]

        # 每类平均分（类基线）
        unique_classes = np.unique(pl_psem)
        avg_score_per_class = {int(cls): float(pl_pscore[pl_psem == cls].mean()) for cls in unique_classes}

        # 全局 keep 掩码（不去重：只筛点）
        keep_mask = np.zeros(N, dtype=bool)

        num_kept, num_dropped, num_kept_major_stuff = 0, 0, 0

        for inst_id in np.unique(pl_pins):
            inst_mask = (pl_pins == inst_id)
            n_inst = int(inst_mask.sum())
            if n_inst < self.min_pts_per_inst:
                # 太小的实例：直接跳过（等价于删除）
                num_dropped += 1
                continue

            sem_this = pl_psem[inst_mask]
            cand_labels, counts = np.unique(sem_this, return_counts=True)
            maj_sem = int(cand_labels[np.argmax(counts)])
            inst_score = float(pl_pscore[inst_mask].mean())

            # 1) 主导为 stuff：整块保留
            if (self.stuff_classes is not None) and (maj_sem in self.stuff_classes):
                keep_mask[inst_mask] = True
                num_kept += 1
                num_kept_major_stuff += 1
                continue

            # 评估语义：单一语义用该类；多语义用主导类
            eval_sem = int(cand_labels[0]) if (len(cand_labels) == 1) else maj_sem
            class_avg = avg_score_per_class.get(eval_sem, 1.0)

            # 2) ignore/stuff 保护（根据开关）
            if (self.ignore_classes is not None) and (eval_sem in self.ignore_classes) and (not self.drop_ignore):
                keep_mask[inst_mask] = True
                num_kept += 1
                continue
            # if (self.stuff_classes is not None) and (eval_sem in self.stuff_classes) and (not self.drop_stuff):
            #     keep_mask[inst_mask] = True
            #     num_kept += 1
            #     continue

            # 3) 低置信实例：双重条件才删除
            drop = (inst_score < self.drop_thr) and (inst_score < class_avg)
            if drop:
                num_dropped += 1
            else:
                keep_mask[inst_mask] = True
                num_kept += 1

        # 若全删，为稳健起见返回原输入并打标记（避免下游炸裂）
        if not np.any(keep_mask):
            input_dict.setdefault('debug', {})
            input_dict['debug']['pl_drop_all_removed'] = True
            return input_dict

        # —— 直接按 keep_mask 同步裁剪所有逐点字段（保持顺序，不去重）——
        input_dict['points'] = points[keep_mask]
        input_dict['pseudo_label']['pts_semantic_mask'] = pl_psem[keep_mask]
        input_dict['pseudo_label']['pts_instance_mask'] = pl_pins[keep_mask]
        input_dict['pseudo_label']['pts_sem_score']     = pl_pscore[keep_mask]
        # 透传 device 等非逐点字段（不裁剪）
        # input_dict['pseudo_label']['device'] 保持原样

        input_dict['sam']['sam_pmask']  = sam_pmask[keep_mask]
        input_dict['sam']['sam_pscore'] = sam_pscore[keep_mask]
        if aug_mask is not None:
            input_dict['aug_mask'] = aug_mask[keep_mask]

        # 统计信息
        input_dict.setdefault('debug', {})
        input_dict['debug']['pl_drop_stats'] = {
            'num_kept_instances': int(num_kept),
            'num_dropped_instances': int(num_dropped),
            'num_kept_major_stuff': int(num_kept_major_stuff),
            'kept_ratio_points': float(np.mean(keep_mask)),
            'drop_thr': self.drop_thr
        }
        return input_dict
