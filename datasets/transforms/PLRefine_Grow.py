import numpy as np
from mmcv.transforms import BaseTransform
from mmdet3d.registry import T<PERSON>NSFORMS
import open3d as o3d
import hdbscan
from collections import Counter
from tools.superpoint import tune_hdbscan_params, process_RANSAC_HDB

# only support nuscenes
@TRANSFORMS.register_module(force=True)
class PLGrow_by_SAMGeo(BaseTransform):
    """根据SAM和几何信息来grow pseudo label。
    与PLGrow_by_GTsi不同，这个版本使用SAM mask和几何聚类信息，
    而不是GT的semantic和instance mask。
    
    Args:
        ignore_label (int): 忽略的标签值，默认为0。
        voxel_size (float): 体素大小，默认为0.05。
        dist_thresh (float): RANSAC平面阈值，默认为0.20。
        iou_thr (float): IoU阈值，默认为0.3。
    """
    def __init__(self, noise_cls, uninterest_cls, 
                 voxel_size: float = 0.05, dist_thresh: float = 0.20, iou_thr: float = 0.3):
        self.ignore_label = list(uninterest_cls) + list(noise_cls)
        self.voxel_size = voxel_size
        self.dist_thresh = dist_thresh
        self.iou_thr = iou_thr

    def transform(self, input_dict):
        """
        Args:
            input_dict (dict): 输入字典，必须包含以下键：
                - points: 点云坐标 (N, 3)
                - pseudo_label.pts_semantic_mask: 伪标签语义标签
                - pseudo_label.pts_sem_score: 伪标签置信度分数
                - sam.sam_pmask: SAM实例mask
                - sam.sam_pscore: SAM置信度分数

        Returns:
            dict: 处理后的输入字典，包含grown的语义和实例标签。
        """
        # 获取输入数据
        points = input_dict['points'][:, :3]  # 只取xyz坐标
        pl_sem = input_dict['pseudo_label']['pts_semantic_mask']
        pl_score = input_dict['pseudo_label']['pts_sem_score']
        sam_pmask = input_dict['sam']['sam_pmask'].copy()
        sam_pscore = input_dict['sam']['sam_pscore'].copy()

        # # 1. 计算高置信度点掩码（per-class median）
        # high_conf_mask = np.zeros_like(pl_sem, dtype=bool)
        # for cls in np.unique(pl_sem):
        #     if cls == self.ignore_label:
        #         continue
        #     cls_pts = pl_sem == cls
        #     if not np.any(cls_pts):
        #         continue
        #     median = np.median(pl_score[cls_pts])
        #     thresh = min(median, 0.9)
        #     high_conf_mask |= cls_pts & (pl_score > thresh)
        
        # # 若无高置信度点，直接返回空mask
        # if not np.any(high_conf_mask):
        #     grown_sem = np.zeros_like(pl_sem)
        #     grown_inst = np.zeros_like(pl_sem)
        #     input_dict['pseudo_label']['pts_semantic_mask'] = grown_sem
        #     input_dict['pseudo_label']['pts_instance_mask'] = grown_inst
        #     return input_dict

        # 2. 处理几何mask
        geo_ins, geo_ground = process_RANSAC_HDB(
            points, 
            voxel_size=self.voxel_size,
            dist_thresh=self.dist_thresh,
            ground_mode="single"
        )
        geo_ins = geo_ins + 1  # 确保实例ID从1开始

        # 3. 融合几何mask
        grown_ins_mask, grown_sem_mask = self._basic_fuse_seed(
            pl_sem, geo_ins, pl_sem, iou_thr=self.iou_thr, ignore_cls=self.ignore_label)

        # 4. 处理SAM mask
        SAM_inst = sam_pmask * ~geo_ground
        SAM_score = sam_pscore * ~geo_ground
        _, SAM_inst = np.unique(SAM_inst, return_inverse=True)  # 重新标记实例
        SAM_score = self._lift_scores_numpy(SAM_score, SAM_inst)  # 提升分数

        # 5. 融合SAM mask
        grown_ins_mask, grown_sem_mask = self._basic_fuse_seed(
            grown_ins_mask, SAM_inst, grown_sem_mask, iou_thr=self.iou_thr, ignore_cls=self.ignore_label)

        # 更新pseudo_label中的标签
        input_dict['pseudo_label']['pts_semantic_mask'] = grown_sem_mask
        input_dict['pseudo_label']['pts_instance_mask'] = grown_ins_mask

        return input_dict

    def _lift_scores_numpy(self, sem_score: np.ndarray, inst_ids: np.ndarray):
        """将语义分数提升到实例级别"""
        if sem_score.ndim == 1:
            sem_score = sem_score.reshape(-1, 1)
        
        unique_inst = np.unique(inst_ids)
        lifted_score = np.zeros_like(sem_score)
        
        for inst_id in unique_inst:
            if inst_id == 0:  # 跳过背景
                continue
            mask = inst_ids == inst_id
            if np.any(mask):
                # 使用该实例内所有点的平均分数
                avg_score = np.mean(sem_score[mask], axis=0)
                lifted_score[mask] = avg_score
                
        return lifted_score.squeeze()

    def _iou(self, mask_a: np.ndarray, mask_b: np.ndarray) -> float:
        """计算两个掩码之间的IoU"""
        inter = np.logical_and(mask_a, mask_b).sum()
        if inter == 0:
            return 0.0
        union = mask_a.sum() + mask_b.sum() - inter
        return inter / union

    def _instance_masks_from_ids(self, inst_ids: np.ndarray):
        """从实例ID生成掩码字典"""
        uniq = np.unique(inst_ids)
        masks = {}
        for iid in uniq:
            if iid <= 0:
                continue
            masks[iid] = inst_ids == iid
        return uniq, masks

    def _get_sem_label(self, sem_map, mask):
        """从语义预测中统计mask区域内出现最多的标签"""
        labels = sem_map[mask]
        if labels.size == 0:
            return 0
        cnt = Counter(labels.ravel())
        # 排除背景 0，如果都只有 0 则返回 0
        cnt.pop(0, None)
        return cnt.most_common(1)[0][0] if cnt else 0

    def _basic_fuse_seed(self, pred_inst: np.ndarray, geo_inst: np.ndarray, 
                        pred_sem: np.ndarray, iou_thr: float = 0.3, ignore_cls: list = []):
        """简单的种子融合：从预测种子 + IoU增长与几何掩码"""
        N = pred_inst.shape[0]
        final_ins = np.full(N, 0, dtype=int)
        final_sem = pred_sem.copy()
        
        # 种子选择
        seed = pred_inst
        
        # 通过几何掩码增长
        _, geo_masks = self._instance_masks_from_ids(geo_inst)
        
        for seed_id in np.unique(seed):
            if seed_id == 0:
                continue
            mask = seed == seed_id
            sem_label = self._get_sem_label(pred_sem, mask)
            if sem_label in ignore_cls:
                continue

            best_gid, best_iou = None, 0.0
            for gid, gmask in geo_masks.items():
                iou_val = self._iou(mask, gmask)
                if iou_val > best_iou:
                    best_iou, best_gid = iou_val, gid
                    
            if best_gid is not None and best_iou >= iou_thr:
                gmask = geo_masks[best_gid]
                grown_mask = np.logical_or(mask, gmask)
                final_ins[grown_mask] = seed_id
                final_sem[grown_mask] = sem_label
            else:
                final_ins[mask] = seed_id
                final_sem[mask] = sem_label
                
        return final_ins, final_sem
