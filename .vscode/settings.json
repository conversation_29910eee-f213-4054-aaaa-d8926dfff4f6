{
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    // Exclude specific folders from the workspace
    "files.watcherExclude": {
      "**/node_modules": true,
      "**/.git": true,
      "**/build": true,
      "**/dist": true,
      "**/__pycache__": true
    },
    "files.exclude": {
      "**/node_modules": true,
      "**/build": true,
      "**/dist": true
    },
    "search.exclude": {
      "**/node_modules": true,
      "**/build": true,
      "**/dist": true
    }
}