import os
import sys
# sys.path.append(os.path.join(os.getcwd(), "/.."))
import numpy as np
import torch
import cv2
import h5py
import pickle
import matplotlib.pyplot as plt
from typing import List, Optional, Sequence, Tuple, Union

# import mmcv
# from mmcv.transforms import BaseTransform
# from mmdet3d.registry import TRANSFORMS
# from mmdet3d.structures.points import BasePoints

# from tools.general_tool import load_point, load_mask, load_viewimages_meta, load_viewimages
from tools.pkl_utils import load_data_skitti, load_viewimages_skitti
from tools.pkl_utils import load_point_mask_viewimages
from tools.projection.pc2img import map_pointcloud_to_image_2
from tools.projection.pc2img import proj_lidar2img
from tools.projection.img_aug import merge_images_yaw, fit_box_cv, expand_box, crop_box_img, paste_box_img, merge_images_pitch_torch, fit_to_box
from tools.projection.pc2img import get_lidar2img

from tools.projection.pc2img import pack_lidar2imgs, pack_lidar2imgs_skitti
from tools.general_tool import load_point, load_mask, load_viewimages
from tools.pkl_utils import get_image_metas
from tools.heatmap_convertor import cand_recall_calc


from nuscenes.nuscenes import NuScenes
from tools.projection.pc2img import _LidarPointCloud
from pyquaternion import Quaternion

proj_path = os.getcwd()
dataroot = proj_path+'/data/skitti'
pklfile = os.path.join(proj_path, 'data/skitti/semantickitti_infos_val.pkl')

# with open(pklfile, 'rb') as f:
#     data = pickle.load(f)

# res_list = load_point_mask_viewimages(data, dataroot)

class_names = [
            'car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist',
            'motorcyclist', 'road', 'parking', 'sidewalk', 'other-ground', 'building',
            'fence', 'vegetation', 'trunck', 'terrian', 'pole', 'traffic-sign'
        ]
color_dict = {
    0: [245, 150, 100], # car, orange
    1: [245, 230, 100], # bicycle, yellow
    2: [150, 60, 30], # motorcycle, brown
    3: [180, 30, 80], # truck, pink
    4: [250, 80, 100], # other-vehicle, red
    5: [30, 30, 255], # person, blue
    6: [200, 40, 255], # bicyclist, purple
    7: [90, 30, 150], # motorcyclist, dark purple
    8: [255, 0, 255], # road, pink
    9: [255, 150, 255], # parking, light pink
    10: [75, 0, 75], # sidewalk, dark gray
    11: [75, 0, 175], # other-ground, purple
    12: [0, 200, 255], # building, light blue
    13: [50, 120, 255], # fence, light blue
    14: [0, 175, 0], # vegetation, green
    15: [0, 60, 135], # trunk, dark green
    16: [80, 240, 150], # terrain, light green
    17: [150, 240, 255], # pole, light blue
    18: [0, 0, 255], # traffic-sign, blue
    19: [0, 0, 0] # unlabeled, black
    }
ignore_index = 19
labels_map = {
        0: 19,  # "unlabeled"
        1: 19,  # "outlier" mapped to "unlabeled" --------------mapped
        10: 0,  # "car"
        11: 1,  # "bicycle"
        13: 4,  # "bus" mapped to "other-vehicle" --------------mapped
        15: 2,  # "motorcycle"
        16: 4,  # "on-rails" mapped to "other-vehicle" ---------mapped
        18: 3,  # "truck"
        20: 4,  # "other-vehicle"
        30: 5,  # "person"
        31: 6,  # "bicyclist"
        32: 7,  # "motorcyclist"
        40: 8,  # "road"
        44: 9,  # "parking"
        48: 10,  # "sidewalk"
        49: 11,  # "other-ground"
        50: 12,  # "building"
        51: 13,  # "fence"
        52: 19,  # "other-structure" mapped to "unlabeled" ------mapped
        60: 8,  # "lane-marking" to "road" ---------------------mapped
        70: 14,  # "vegetation"
        71: 15,  # "trunk"
        72: 16,  # "terrain"
        80: 17,  # "pole"
        81: 18,  # "traffic-sign"
        99: 19,  # "other-object" to "unlabeled" ----------------mapped
        252: 0,  # "moving-car" to "car" ------------------------mapped
        253: 6,  # "moving-bicyclist" to "bicyclist" ------------mapped
        254: 5,  # "moving-person" to "person" ------------------mapped
        255: 7,  # "moving-motorcyclist" to "motorcyclist" ------mapped
        256: 4,  # "moving-on-rails" mapped to "other-vehic------mapped
        257: 4,  # "moving-bus" mapped to "other-vehicle" -------mapped
        258: 3,  # "moving-truck" to "truck" --------------------mapped
        259: 4  # "moving-other"-vehicle to "other-vehicle"-----mapped
    }
    

# from tools.general_tool import load_point, load_mask, load_viewimages
# from tools.pkl_utils import get_image_metas

# def load_data_skitti(pklfile, dataroot, load_point=False, load_mask=False, load_pic=False):
#     with open(pklfile, 'rb') as f:
#         data = pickle.load(f)
        
#     res_list = []
#     for i in range(len(data['data_list'])):
#         res = {}
#         # load LiDAR point
#         lidar_path = os.path.join(dataroot, data['data_list'][i]['lidar_points']['lidar_path'])
#         load_dim = data['data_list'][i]['lidar_points']['num_pts_feats']
#         res['lidar_path'] = lidar_path
#         if load_point:
#             points = load_point(lidar_path, load_dim=load_dim, use_dim=range(load_dim))
#             res['points'] = points
#         # load semantic and instance mask
#         pano_mask_path = os.path.join(dataroot, data['data_list'][i]['pts_panoptic_mask_path'])
#         res['pano_mask_path'] = pano_mask_path
#         if load_mask:
#             try:
#                 sem_mask, ins_mask = load_mask(pano_mask_path, dataset_type='semantickitti')
#                 res['pts_semantic_mask'], res['pts_instance_mask'] = sem_mask, ins_mask
                
#             except:
#                 print('Failed to load mask from {}'.format(pano_mask_path))
#         # load view images
#         img_metas = get_image_metas(dataroot, lidar_path)
#         if load_pic:
#             img_metas = load_viewimages_skitti(img_metas, crop=True, target_height=360, crop_size=(360, 640))
#         res['imgs_meta'] = img_metas
#         # generate sample token
#         #TODO
#         res_list.append(res)
#     return res_list


# load all
# def load_data_skitti(pklfile, dataroot):
#     with open(pklfile, 'rb') as f:
#         data = pickle.load(f)
        
#     res_list = []
#     for i in range(len(data['data_list'])):
#         res = {}
#         # load LiDAR point
#         lidar_path = os.path.join(dataroot, data['data_list'][i]['lidar_points']['lidar_path'])
#         load_dim = data['data_list'][i]['lidar_points']['num_pts_feats']
#         points = load_point(lidar_path, load_dim=load_dim, use_dim=range(load_dim))
#         res['lidar_path'] = lidar_path
#         res['points'] = points
#         # load semantic and instance mask
#         pano_mask_path = os.path.join(dataroot, data['data_list'][i]['pts_panoptic_mask_path'])
#         sem_mask, ins_mask = load_mask(pano_mask_path, dataset_type='semantickitti')
#         res['pts_semantic_mask'], res['pts_instance_mask'] = sem_mask, ins_mask
#         # load view images
#         img_metas = get_image_metas(dataroot, lidar_path)
#         img_metas = load_viewimages_skitti(img_metas, crop=True, target_height=360, crop_size=(360, 640))
#         res['imgs_meta'] = img_metas
#         # generate sample token
#         #TODO
#         res_list.append(res)
#     return res_list

res_list = load_data_skitti(pklfile, dataroot, load_pic=False, load_mask=False, load_point=False)
len(res_list)

def plot_point_in_camview_2(points, coloring, im, dot_size=5):
    import matplotlib.pyplot as plt

    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    # fig.canvas.set_window_title('Point cloud in camera view')

    ax.imshow(im)
    ax.scatter(points[:, 0], points[:, 1], c=coloring, s=dot_size)
    ax.axis('off')
    return fig, ax

i = 0
crop = False
res = res_list[i]
if 'points' in res.keys():
    points = res['points'].copy()
else:
    lidar_path = res['lidar_path']
    points = load_point(lidar_path, load_dim=4, use_dim=range(4))
    res['points'] = points
if 'pts_semantic_mask' in res.keys():
    sem_mask = res['pts_semantic_mask'].copy()
    ins_mask = res['pts_instance_mask'].copy()
else:
    pano_mask_path = res['pano_mask_path']
    sem_mask, ins_mask = load_mask(pano_mask_path, dataset_type='semantickitti')
    res['pts_semantic_mask'], res['pts_instance_mask'] = sem_mask, ins_mask
img_metas = res['imgs_meta'].copy()

for img_id in ['image_2', 'image_3']:
    if img_id not in img_metas.keys():
        img_metas = load_viewimages_skitti(img_metas, crop=crop, target_height=360, crop_size=(360, 640))
    img_meta = img_metas[img_id]
    img = img_meta['img']
    img_size = (img.shape[1], img.shape[0])
    lidar2img = img_meta['lidar2img']
    offset = None
    if crop:
        img_ori = plt.imread(img_meta['img_path'])
        ori_width = img_ori.shape[1]
        ori_size = img.shape
        scale = img.shape[0]/img_ori.shape[0]
        img_size = (img.shape[1]/scale, img.shape[0]/scale)
        # print(scale, img_ori.shape[0], img.shape[0])
        if img_id == 'image_3':
            offset = [ori_width-ori_size[1]/scale, ori_width]
            
        else:
            offset = None
    points = points[:, :3]
    poi, mask = proj_lidar2img(points, lidar2img, img_size=img_size, min_dist=1.0, offset=offset)
    if crop:
        poi = poi * scale
    sem_mask_ = sem_mask[mask]
    coloring = np.array([color_dict[i] for i in sem_mask_])/255
    
    plot_point_in_camview_2(poi, coloring, img, dot_size=3)
    plt.show()

import os
import argparse
import sys
import copy
import numpy as np
import torch
import argparse
from PIL import Image, ImageDraw, ImageFont
from torchvision.ops import box_convert
import pickle
from tqdm import tqdm
from nuscenes.nuscenes import NuScenes

# Grounding DINO
sys.path.append('/home/<USER>/codefield/misc/')
import groundingdino.datasets.transforms as T
from groundingdino.models import build_model
from groundingdino.util import box_ops
from groundingdino.util.slconfig import SLConfig
from groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from groundingdino.util.inference import annotate, load_image, predict
from huggingface_hub import hf_hub_download

# segment anything
from segment_anything import build_sam, SamPredictor 
import cv2
import numpy as np
import matplotlib.pyplot as plt
from Grouding_SAM_Utils import load_image_from_np, load_model_hf, plot_anns, pack_anno

# load pkl
sys.path.append('tools')
# from pkl_utils import load_viewimages_path

import numpy as np
import matplotlib.pyplot as plt

class PointCloudVisualizer:
    def __init__(self, out_dict, color_map, save_path='output/rerun_samples/', dataset_type='semantickitti'):
        self.points = out_dict['points'].copy()
        if 'pts_semantic_mask' in out_dict:
            self.sem_mask = out_dict['pts_semantic_mask'].copy()
        if 'augment_mask' in out_dict:
            self.aug_mask = out_dict['augment_mask'].copy()
        if 'pts_instance_mask' in out_dict:
            self.inst_mask = out_dict['pts_instance_mask'].copy()
        if 'pred_inst' in out_dict:
            self.pred_inst = out_dict['pred_inst'].copy()
        self.color_map = color_map
        self.save_path = save_path
        if dataset_type == 'semantickitti':
            self.ignore_class = 19
            self.thing_classes = range(8)
        elif dataset_type == 'nuscenes':
            self.ignore_class = 0
            self.thing_classes = range(1, 11)

    def prepare_semantic_colors(self):
        return np.array([self.color_map[i] for i in self.sem_mask]) / 255
    
    def prepare_semantic_thing_colors(self):
        sem_mask_thing = self.sem_mask.copy()
        # nusc
        # sem_mask_thing[(sem_mask_thing==0) | (sem_mask_thing>=10)] = 0
        # skitti
        # print(f'NOTE: Skitti semantic label is different from nuscenes. Please check the label mapping.')
        sem_mask_thing[~np.isin(sem_mask_thing, self.thing_classes)] = self.ignore_class
        return np.array([self.color_map[i] for i in sem_mask_thing]) / 255

    def prepare_augment_colors(self):
        aug_color = np.array([[255, 0, 0], [0, 0, 255]]) # 0: red, 1: blue
        aug_mask = self.aug_mask.astype(np.int8)
        return aug_color[aug_mask] / 255

    def prepare_instance_colors(self, inst_mask, filter_stuffs=False):
        coloring_inst = np.ones((inst_mask.shape[0], 3))
        # colorize the background to light gray
        coloring_inst = coloring_inst* np.array([200, 200, 200]) / 255
        num_inst = 0
        for inst_id in np.unique(inst_mask):
            if inst_id == 0 or (filter_stuffs and np.unique(self.sem_mask[inst_mask == inst_id])[0] not in self.thing_classes):
                # print(f'Ignore instance {inst_id}, semantic label: {np.unique(self.sem_mask[inst_mask == inst_id])[0]} due to {inst_id == 0} or {np.unique(self.sem_mask[inst_mask == inst_id])[0] not in self.thing_classes')
                continue
            coloring_inst[inst_mask == inst_id] = np.random.randint(0, 255, 3) / 255
            num_inst += 1
        return coloring_inst, num_inst

    def save_data(self):
        np.save(self.save_path + 'points.npy', self.points)
        np.save(self.save_path + 'sem_mask.npy', self.sem_mask)
        np.save(self.save_path + 'aug_mask.npy', self.aug_mask)

    def plot(self, color_mode='instance', mode='2d', custom_input=None, filter_stuffs=False):
        assert mode in ['2d', '3d'], "Invalid mode. Choose from '2d' or '3d'."
        if color_mode == 'semantic':
            colors = self.prepare_semantic_colors()
        elif color_mode == 'semantic_thing':
            colors = self.prepare_semantic_thing_colors()
        elif color_mode == 'augment':
            colors = self.prepare_augment_colors()
        elif color_mode == 'instance':
            colors, num_inst = self.prepare_instance_colors(self.inst_mask, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        elif color_mode == 'pred_instance':
            colors, num_inst = self.prepare_instance_colors(self.pred_inst, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        elif color_mode == 'custom':
            colors, num_inst = self.prepare_instance_colors(custom_input, filter_stuffs)
            print(f'Number of instances: {num_inst}')
        else:
            raise ValueError("Invalid color_mode. Choose from 'semantic', 'augment', or 'instance'.")

        fig = plt.figure(figsize=(10, 10))
        if mode == '2d':
            fig = plt.figure(figsize=(10, 10))
            ax = fig.add_subplot(111)
            ax.scatter(self.points[:, 0], self.points[:, 1], c=colors, s=1)
            ax.set_xlabel('X Label')
            ax.set_ylabel('Y Label')
        else:
            ax = fig.add_subplot(111, projection=mode, proj_type='ortho')
            ax.scatter(self.points[:, 0], self.points[:, 1], self.points[:, 2], c=colors, s=3)
            ax.set_xlabel('X Label')
            ax.set_ylabel('Y Label')
            ax.set_zlabel('Z Label')
        plt.show()


def filter_3d_points_in_mask(points_3d, poi, sam_mask, view_mask):
    """
    筛选落在 2D mask 中的 3D 点。

    参数:
        points_3d (torch.Tensor): 形状为 (N, 3) 的 3D 点云。
        poi (torch.Tensor): 形状为 (N, 2) 的投影点云坐标 (x, y)。
        mask (torch.Tensor): 形状为 (n, H, W) 的二值化 mask 图像。

    返回:
        torch.Tensor: 落在 mask 中的 3D 点。
    """
    poi = poi.round().to(torch.int64)

    n, h, w = sam_mask.shape
    # valid_indices = (poi[:, 0] >= 0) & (poi[:, 0] < w) & (poi[:, 1] >= 0) & (poi[:, 1] < h)
    # check coordinates bounds
    assert (poi[:, 0] >= 0).all() and (poi[:, 0] < w).all() and (poi[:, 1] >= 0).all() and (poi[:, 1] < h).all()
    N = points_3d.shape[0]
    sel_idx = torch.arange(0, N).to(poi.device)
    
    # 初始化结果存储
    valid_indices = []

    # 遍历每个 mask 层
    for i in range(n):
        current_mask = sam_mask[i]
        # valid_poi = poi[valid_indices]
        # valid_points = points_3d[view_mask]
        valid_idx = sel_idx[view_mask]
        assert valid_idx.shape[0] == poi.shape[0]

        # 筛选 mask 内的点
        mask_indices = current_mask[poi[:, 1], poi[:, 0]] == 1
        valid_indices.append(valid_idx[mask_indices])

    # 合并所有结果
    # filtered_points = torch.cat(filtered_points, dim=0) if filtered_points else torch.empty((0, 3), device=points_3d.device)
    return valid_indices


# hyperparameters
BOX_TRESHOLD = 0.1
TEXT_TRESHOLD = 0.2
all_texts = [
        'car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist',
        'motorcyclist', 'road', 'parking', 'sidewalk', 'other-ground', 'building',
        'fence', 'vegetation', 'trunck', 'terrian', 'pole', 'traffic-sign'
    ]
thing_texts = ['car', 'bicycle', 'motorcycle', 'truck', 'bus', 'person', 'bicyclist', 'motorcyclist']

VIEWS = ['image_2', 'image_3']
LEFT_VIEW = ['image_2']
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


# load models
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
ckpt_repo_id = "ShilongLiu/GroundingDINO"
ckpt_filenmae = "groundingdino_swinb_cogcoor.pth"
ckpt_config_filename = "GroundingDINO_SwinB.cfg.py"
groundingdino_model = load_model_hf(ckpt_repo_id, ckpt_filenmae, ckpt_config_filename)

sam_checkpoint_path = 'checkpoints/sam/sam_vit_h_4b8939.pth'
sam_checkpoint = sam_checkpoint_path
sam = build_sam(checkpoint=sam_checkpoint)
sam.to(device=DEVICE)
sam_predictor = SamPredictor(sam)

crop = False # not influence the SAM input currently
failsamples = []
sam_name = 'sam_B1T2'

for i in tqdm(range(0, 20)):
# for i in tqdm(range(len(res_list))):
    # print(f'Processing {i}: {res_list[i]["lidar_path"]}')
    #### mkdir ###
    folder_path = res_list[i]['lidar_path'].split('velodyne')[0]+f'{sam_name}/'
    if not os.path.exists(folder_path): 
        os.mkdir(folder_path) 
    for p in ['sam_ground_seg', 'sam_ground_det', 'sam_ground_pth']:
        if not os.path.exists(folder_path+p):
            os.mkdir(folder_path+p)
        for v in LEFT_VIEW:
            path = folder_path+p+'/'+v
            if not os.path.exists(path):
                print(f'Make dir: {path}')
                os.mkdir(path) 
    for v in LEFT_VIEW: # cam_front already generated
        # try:
        # try:
        save_seg_path = res_list[i]['lidar_path'].replace('velodyne', f'{sam_name}/sam_ground_seg/{v}').replace('.bin', '.png')
        save_det_path = res_list[i]['lidar_path'].replace('velodyne', f'{sam_name}/sam_ground_det/{v}').replace('.bin', '.png')
        save_pth_path = res_list[i]['lidar_path'].replace('velodyne', f'{sam_name}/sam_ground_pth/{v}').replace('.bin', '.pth')
        
        # print(f'Save seg path: {save_seg_path}')
        # print(f'Save det path: {save_det_path}')
        # print(f'Save pth path: {save_pth_path}')
        # if os.path.exists(save_pth_path):
        #     print(f'Skip file: {save_pth_path} already exists.')
        #     failsamples.append(save_pth_path)
        #     continue
        
        # print('Start inferencing...')
        img_meta = load_viewimages_skitti(res_list[i]['imgs_meta'], crop=crop, target_height=360, crop_size=(360, 640))
        image_path = img_meta[v]['img_path']
        image_source, image = load_image(image_path)
        H, W, _ = image_source.shape
        target_bboxes, obj_labels = [], []
        annotated_frame = image_source.copy()
        for TEXT_PROMPT in thing_texts:                
            boxes, logits, phrases = predict(
                model=groundingdino_model, 
                image=image, 
                caption=TEXT_PROMPT, 
                box_threshold=BOX_TRESHOLD, 
                text_threshold=TEXT_TRESHOLD,
                device=DEVICE
            )

            annotated_frame = annotate(image_source=annotated_frame, boxes=boxes, logits=logits, phrases=phrases)
            annotated_frame = annotated_frame[...,::-1] # BGR to RGB
            boxes_xyxy = box_ops.box_cxcywh_to_xyxy(boxes) * torch.Tensor([W, H, W, H])
            target_bboxes.append(boxes_xyxy)
            for _ in range(boxes_xyxy.shape[0]):
                obj_labels.append(TEXT_PROMPT)
            
        if len(target_bboxes) == 0:
            img = plt.imread(image_path)
            plt.imsave(save_det_path, img)
            plt.imsave(save_seg_path, img)
            mask = np.zeros((0, H, W), dtype=np.uint8)
            torch.save({'masks': mask, 'scores': torch.tensor([]), 'logits': torch.tensor([]), 'target_bboxes': torch.tensor([]), 'obj_labels': []}, save_pth_path)
            print(f'No detection found in {image_path}.')
            continue
        
        plt.figure(figsize=(16, 9))
        plt.imshow(annotated_frame)
        plt.axis('off')
        plt.savefig(save_det_path)
        plt.close()

        target_bboxes = torch.cat(target_bboxes, dim=0)

        # set image
        sam_predictor.set_image(image_source)
        transformed_boxes = sam_predictor.transform.apply_boxes_torch(target_bboxes, image_source.shape[:2]).to(DEVICE)
        masks, scores, logits = sam_predictor.predict_torch(
                    point_coords = None,
                    point_labels = None,
                    boxes = transformed_boxes,
                    multimask_output = False,
                )
        
        masks_ = masks.squeeze(1)
        res = plot_anns(pack_anno(masks_, scores, target_bboxes, obj_labels), image_source) # show all masks on the image, w/o bbox
        plt.figure(figsize=(16, 9))
        plt.imshow(res)
        plt.axis('off')
        plt.savefig(save_seg_path)
        plt.close()
        
        masks_ = masks_.cpu()
        scores = scores.cpu()
        logits = logits.cpu()
        target_bboxes = target_bboxes.cpu()
        
        torch.save({'masks': masks_, 'scores': scores, 'logits': logits, 'target_bboxes': target_bboxes, 'obj_labels': obj_labels}, save_pth_path)
        torch.save(failsamples, 'failsamples.pth')
        # except Exception as e:
        #     print(f'Error: {e}')
            # continue

crop = False
missed = []
do_cluster = False

img_meta = load_viewimages_skitti(img_metas, crop=crop, target_height=360, crop_size=(360, 640))
img_meta['image_2']['img'].shape

from tools.cluster_utils import create_mask, create_mask_ref, cluster_points_with_dbscan, get_comfusion_matrix
# DEVICE = 'cpu'
recall_list = np.zeros((len(res_list), 2))
for i in tqdm(range(20)):
# for i in tqdm(range(len(res_list))):
    res = res_list[i]
    if 'points' in res.keys():
        points = res['points'].copy()
    else:
        lidar_path = res['lidar_path']
        points = load_point(lidar_path, load_dim=4, use_dim=range(4))
        res['points'] = points
    if 'pts_semantic_mask' in res.keys():
        sem_mask = res['pts_semantic_mask'].copy()
        ins_mask = res['pts_instance_mask'].copy()
    else:
        pano_mask_path = res['pano_mask_path']
        sem_mask, ins_mask = load_mask(pano_mask_path, dataset_type='semantickitti')
        res['pts_semantic_mask'], res['pts_instance_mask'] = sem_mask, ins_mask
    img_metas = res['imgs_meta'].copy()

    
    # init the cluster dict
    N = points.shape[0]        
    lidar_idx = torch.arange(0, N).to(DEVICE)
    lidar_cluster = {}
    lidar_cluster_labels = {}
    cluster_num = 0
    
    save_path = res['lidar_path'].replace('velodyne', f'{sam_name}/pred_inst_pts').replace('.bin', '.h5')
    if not os.path.exists(os.path.dirname(save_path)):
        os.makedirs(os.path.dirname(save_path))
    # if os.path.exists(save_path):
    #     continue
    
    pts_inst_cluster = torch.zeros((N)).to(DEVICE)
    pts_inst_cluster_color = torch.zeros((N,3)).to(DEVICE)
    
    for view in LEFT_VIEW:

        # project point cloud to camera view
        # if view not in img_metas.keys():
        img_metas = load_viewimages_skitti(img_metas, crop=False) #, target_height=360, crop_size=(360, 640)
        img_meta = img_metas[view]
        img = img_meta['img']
        img_size = (img.shape[1], img.shape[0])
        lidar2img = img_meta['lidar2img']
        offset = None
        if crop:
            img_ori = plt.imread(img_meta['img_path'])
            ori_width = img_ori.shape[1]
            ori_size = img.shape
            scale = img.shape[0]/img_ori.shape[0]
            img_size = (img.shape[1]/scale, img.shape[0]/scale)
            # print(scale, img_ori.shape[0], img.shape[0])
            if view == 'image_3':
                offset = [ori_width-ori_size[1]/scale, ori_width]
                
            else:
                offset = None
        
        points = points[:, :3]
        poi, mask = proj_lidar2img(points, lidar2img, img_size=img_size, min_dist=1.0, offset=offset)
        if crop:
            poi = poi * scale
        sem_mask_ = sem_mask[mask]
        coloring = np.array([color_dict[i] for i in sem_mask_])/255
        coor = points[mask]
        coor = torch.tensor(coor).to(DEVICE).long()
        poi = torch.tensor(poi).to(DEVICE).long()
        
        # plot_point_in_camview_2(poi, coloring, img, dot_size=3)
        # plt.show()
        
        # load the segmentation results
        mask_path = res['lidar_path'].replace('velodyne', f'{sam_name}/sam_ground_pth/{view}').replace('.bin', '.pth')
        if not os.path.exists(mask_path):
            missed.append(res['lidar_path'])
            continue
        sam_mask = torch.load(mask_path)
        sam_masks = sam_mask['masks'].to(DEVICE)
        if torch.unique(sam_masks).shape[0] == 1:
            with h5py.File(save_path, 'w') as f:
                f.create_dataset('mask', data=pts_inst_cluster.cpu().numpy().astype(np.uint16), compression='gzip')
            continue
        act_points = filter_3d_points_in_mask(points, poi, sam_masks, mask)
        act_points = sorted(act_points, key=lambda x: len(x), reverse=True) # sort by the size for overlapping
        assert not do_cluster, "Not implemented yet."
        for act_point in act_points:
            pts_inst_cluster[act_point] = cluster_num
            cluster_num += 1

    # save the cluster results
    with h5py.File(save_path, 'w') as f:
        f.create_dataset('mask', data=pts_inst_cluster.cpu().numpy().astype(np.uint16), compression='gzip')

    # quality evaluation: recall
    view_gt = ins_mask.copy()
    view_gt[~mask] = 0
    view_pred = pts_inst_cluster.cpu().numpy().copy()
    recall_res = cand_recall_calc(view_gt, view_pred)
    recall_list[i][0] = recall_res.sum()    
    recall_list[i][1] = recall_res.shape[0]
    
    res['pred_inst'] = pts_inst_cluster.cpu().numpy()
    # res['pred_inst_color'] = pts_inst_cluster_color.cpu().numpy()
    res['view_mask'] = mask
    
print(f'Missed samples: {missed}')
print(f'Recall: {recall_list[:,0].sum()/recall_list[:,1].sum()}')   

recall_list


view_gt = ins_mask.copy()
view_gt[~mask] = 0

view_pred = pts_inst_cluster.cpu().numpy().copy()
recall_res = cand_recall_calc(view_gt, view_pred)
recall_value = recall_res.sum()/recall_res.shape[0]
precision_value = recall_res.sum()/np.unique(view_pred).shape[0]    
print(f'Recall: {recall_value}, Precision: {precision_value}')

# thre = 0
# view_pred = torch.tensor(view_pred)
# view_gt = torch.tensor(view_gt)
# unique_gt, gt_inst_reindexed = torch.unique(view_gt, return_inverse=True)
# M = unique_gt.shape[0]

# gt_inst_onehot = torch.zeros((gt_inst_reindexed.shape[0], M), device=gt_inst_reindexed.device)
# gt_inst_onehot.scatter_(1, gt_inst_reindexed.unsqueeze(1), 1)
# overlaps = torch.einsum('pm,p->pm', gt_inst_onehot, view_pred.float())
# recall_res = overlaps.max(dim=0)[0] > thre
# recall = recall_res.sum().item() / M

# # Precision
# precision_res = overlaps.max(dim=1)[0] > thre
# precision = precision_res.sum().item() / view_pred.sum().item()

# recall, precision

recall = recall_res.sum().item() / M

# Precision
precision_res = overlaps.max(dim=1)[0] > thre
precision = precision_res.sum().item() / pred_cand.sum().item()

return recall, precision





# 示例数据
points_3d = torch.from_numpy(points).to(poi)
# poi = poi
# mask = sam_masks.clone() 
view_mask = mask
# 调用函数
act_points = filter_3d_points_in_mask(points_3d, poi, sam_masks, view_mask)
# print(f"落在 mask 中的 3D 点数: {filtered_points.shape[0]}")
# sort the points by size from large to small
act_points = sorted(act_points, key=lambda x: len(x), reverse=True)
# add each cluster to the lidar_cluster
cluster_num = 0
for act_point in act_points:
    lidar_cluster[cluster_num] = act_point
    cluster_num += 1

# do cluster
visualizer = PointCloudVisualizer(res_list[0], color_dict)
clusters = []
for i in range(len(act_points)):
    obj_mask = np.zeros(points.shape[0])
    obj_mask[act_points[i].cpu().numpy()] = 1
    # if do_cluster: 
    obj = points[act_points[i].cpu().numpy()]
    clustered_res, clustered_labels = cluster_points_with_dbscan(obj, eps=2, min_samples=1)  
    print('********************************')
    print(f'Number of clusters: {np.unique(clustered_labels).shape[0]}')
    # visualizer.plot(color_mode='custom', mode='2d', custom_input=obj_mask, filter_stuffs=False)
    for j in range(np.unique(clustered_labels).shape[0]):
        label = np.unique(clustered_labels)[j]
        obj_mask = np.zeros(points.shape[0])
        obj_mask[act_points[i].cpu().numpy()[clustered_labels==label]] = 1
        print(f'Processing cluster {j}, num of points: {np.sum(obj_mask)}')
        clusters.append(obj_mask)
        visualizer.plot(color_mode='custom', mode='2d', custom_input=obj_mask, filter_stuffs=False)


# # Example usage:
visualizer = PointCloudVisualizer(res_list[0], color_dict, dataset_type='semantickitti')
visualizer.plot(color_mode='semantic', mode='2d')


view_ins_mask = ins_mask.copy()
view_ins_mask[~mask] = 0
print('num inst in front view', np.unique(view_ins_mask).shape)
visualizer = PointCloudVisualizer(res_list[0], color_dict)
visualizer.plot(color_mode='custom', mode='2d', custom_input=view_ins_mask)

visualizer = PointCloudVisualizer(res_list[0], color_dict)
visualizer.plot(color_mode='instance', mode='2d', filter_stuffs=True)

points = res_list[i]['points']
colors = res_list[i]['pred_inst_color']

assert points.shape[0] == colors.shape[0]

fig = plt.figure(figsize=(10, 10))
fig = plt.figure(figsize=(10, 10))
ax = fig.add_subplot(111)
ax.scatter(points[:, 0], points[:, 1], c=colors, s=1)
ax.set_xlabel('X Label')
ax.set_ylabel('Y Label')

np.unique(colors, axis=0)

res_list[i]['pred_inst_color'].shape

